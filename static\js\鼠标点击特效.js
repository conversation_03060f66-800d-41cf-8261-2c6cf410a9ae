// 鼠标点击特效 - 优化版本
(function() {
    'use strict';
    
    // 检查是否已经初始化过
    if (window.clickEffectInitialized) {
        return;
    }
    window.clickEffectInitialized = true;
    
    // 配置参数
    const config = {
        colors: ["#F73859", "#14FFEC", "#00E0FF", "#FF99FE", "#FAF15D"],
        ballCount: {
            normal: { min: 10, max: 20 },
            longPress: { min: 50, max: 100 }
        },
        ballSize: { min: 8, max: 12 },
        longPressDelay: 500,
        canvasZIndex: 99999
    };
    
    let balls = [];
    let longPressed = false;
    let longPressTimer;
    let multiplier = 0;
    let width, height;
    let origin;
    let normal;
    let ctx;
    let animationId;
    
    // 创建画布
    const canvas = document.createElement("canvas");
    canvas.setAttribute("style", `
        width: 100%; 
        height: 100%; 
        top: 0; 
        left: 0; 
        z-index: ${config.canvasZIndex}; 
        position: fixed; 
        pointer-events: none;
        user-select: none;
    `);
    
    // 等待DOM加载完成后添加画布
    function initCanvas() {
        if (document.body) {
            document.body.appendChild(canvas);
            
            if (canvas.getContext && window.addEventListener) {
                ctx = canvas.getContext("2d");
                updateSize();
                bindEvents();
                startLoop();
            }
        } else {
            // 如果DOM还没准备好，延迟初始化
            setTimeout(initCanvas, 100);
        }
    }
    
    // 更新画布尺寸
    function updateSize() {
        const dpr = window.devicePixelRatio || 1;
        canvas.width = window.innerWidth * dpr;
        canvas.height = window.innerHeight * dpr;
        canvas.style.width = window.innerWidth + 'px';
        canvas.style.height = window.innerHeight + 'px';
        ctx.scale(dpr, dpr);
        
        width = window.innerWidth;
        height = window.innerHeight;
        origin = {
            x: width / 2,
            y: height / 2
        };
        normal = {
            x: width / 2,
            y: height / 2
        };
    }
    
    // 绑定事件
    function bindEvents() {
        // 窗口大小改变事件
        window.addEventListener('resize', updateSize, false);
        
        // 鼠标按下事件
        window.addEventListener("mousedown", function(e) {
            if (e.button !== 0) return; // 只处理左键点击
            
            pushBalls(randBetween(config.ballCount.normal.min, config.ballCount.normal.max), e.clientX, e.clientY);
            document.body.classList.add("is-pressed");
            
            longPressTimer = setTimeout(function(){
                document.body.classList.add("is-longpress");
                longPressed = true;
            }, config.longPressDelay);
        }, false);
        
        // 鼠标抬起事件
        window.addEventListener("mouseup", function(e) {
            if (e.button !== 0) return; // 只处理左键点击
            
            clearTimeout(longPressTimer);
            if (longPressed) {
                document.body.classList.remove("is-longpress");
                pushBalls(
                    randBetween(
                        config.ballCount.longPress.min + Math.ceil(multiplier), 
                        config.ballCount.longPress.max + Math.ceil(multiplier)
                    ), 
                    e.clientX, 
                    e.clientY
                );
                longPressed = false;
            }
            document.body.classList.remove("is-pressed");
        }, false);
    }
    
    // 球类
    class Ball {
        constructor(x = origin.x, y = origin.y) {
            this.x = x;
            this.y = y;
            this.angle = Math.PI * 2 * Math.random();
            
            if (longPressed) {
                this.multiplier = randBetween(14 + multiplier, 15 + multiplier);
            } else {
                this.multiplier = randBetween(6, 12);
            }
            
            this.vx = (this.multiplier + Math.random() * 0.5) * Math.cos(this.angle);
            this.vy = (this.multiplier + Math.random() * 0.5) * Math.sin(this.angle);
            this.r = randBetween(config.ballSize.min, config.ballSize.max) + 3 * Math.random();
            this.color = config.colors[Math.floor(Math.random() * config.colors.length)];
        }
        
        update() {
            this.x += this.vx - normal.x;
            this.y += this.vy - normal.y;
            normal.x = -2 / window.innerWidth * Math.sin(this.angle);
            normal.y = -2 / window.innerHeight * Math.cos(this.angle);
            this.r -= 0.3;
            this.vx *= 0.9;
            this.vy *= 0.9;
        }
    }
    
    // 添加球
    function pushBalls(count = 1, x = origin.x, y = origin.y) {
        for (let i = 0; i < count; i++) {
            balls.push(new Ball(x, y));
        }
    }
    
    // 随机数生成
    function randBetween(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }
    
    // 动画循环
    function loop() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        for (let i = 0; i < balls.length; i++) {
            let b = balls[i];
            if (b.r < 0) continue;
            
            ctx.fillStyle = b.color;
            ctx.beginPath();
            ctx.arc(b.x, b.y, b.r, 0, Math.PI * 2, false);
            ctx.fill();
            b.update();
        }
        
        if (longPressed) {
            multiplier += 0.2;
        } else if (!longPressed && multiplier >= 0) {
            multiplier -= 0.4;
        }
        
        removeBalls();
        animationId = requestAnimationFrame(loop);
    }
    
    // 开始动画循环
    function startLoop() {
        if (animationId) {
            cancelAnimationFrame(animationId);
        }
        loop();
    }
    
    // 移除超出边界的球
    function removeBalls() {
        for (let i = balls.length - 1; i >= 0; i--) {
            let b = balls[i];
            if (b.x + b.r < 0 || b.x - b.r > width || 
                b.y + b.r < 0 || b.y - b.r > height || 
                b.r < 0) {
                balls.splice(i, 1);
            }
        }
    }
    
    // 清理函数
    function cleanup() {
        if (animationId) {
            cancelAnimationFrame(animationId);
        }
        if (canvas && canvas.parentNode) {
            canvas.parentNode.removeChild(canvas);
        }
        window.clickEffectInitialized = false;
    }
    
    // 页面卸载时清理
    window.addEventListener('beforeunload', cleanup);
    
    // 初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initCanvas);
    } else {
        initCanvas();
    }
    
})();
