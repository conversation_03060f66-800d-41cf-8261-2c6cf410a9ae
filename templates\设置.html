<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SteamVault Pro - Steam 宝库 - 设置</title>
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
    <link rel="icon" type="image/png" href="/static/favicon.png">
    <link rel="shortcut icon" href="/static/favicon.ico">
    <link rel="stylesheet" href="/static/css/特效样式.css">
    <script src="/static/js/安全防护.js"></script>
    <script src="/static/js/特效管理器.js"></script>
    <style>
        :root {
            --primary-bg: #656f8c;
            --sidebar-bg: #373e54;
            --card-bg: #eef1f8;
            --card-purple: #f7eeff;
            --card-blue: #edf5ff;
            --text-dark: #333;
            --text-light: #666;
            --accent-green: #4CAF50;
            --accent-purple: #8e44ad;
            --accent-blue: #3498db;
            --border-radius: 12px;
            --card-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background-color: var(--primary-bg);
            color: var(--text-dark);
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            background: linear-gradient(135deg, var(--sidebar-bg) 0%, #2c3e50 100%);
            width: 85px;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 25px 0;
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.15);
            position: relative;
            overflow: hidden;
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            pointer-events: none;
        }

        .sidebar-nav {
            display: flex;
            flex-direction: column;
            gap: 12px;
            width: 100%;
            align-items: center;
            padding-top: 10px;
        }

        .sidebar-icon {
            width: 55px;
            height: 55px;
            border-radius: 18px;
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            margin-bottom: 5px;
        }

        .sidebar-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
        }

        .sidebar-icon:hover::before {
            left: 100%;
        }

        .sidebar-icon-symbol {
            font-size: 22px;
            margin-bottom: 2px;
            transition: all 0.3s ease;
        }

        .sidebar-icon-text {
            font-size: 10px;
            font-weight: 500;
            opacity: 0.9;
            transition: all 0.3s ease;
        }

        .sidebar-icon:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.2);
            color: white;
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .sidebar-icon:hover .sidebar-icon-symbol {
            transform: scale(1.1);
        }

        .sidebar-icon.active {
            background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
            border-color: var(--accent-blue);
            color: white;
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
            transform: translateY(-1px);
        }

        .sidebar-icon.active::after {
            content: '';
            position: absolute;
            right: -3px;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 30px;
            background: linear-gradient(to bottom, var(--accent-blue), var(--accent-purple));
            border-radius: 2px;
            box-shadow: 0 0 10px rgba(52, 152, 219, 0.5);
        }

        .sidebar-icon.active .sidebar-icon-symbol {
            transform: scale(1.1);
        }
        
        .main-container {
            flex: 1;
            padding: 30px;
            display: flex;
            flex-direction: column;
            overflow-x: hidden;
        }
        
        .header {
            display: flex;
            flex-direction: column;
            margin-bottom: 30px;
        }
        
        .page-title {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            padding: 25px;
            box-shadow: var(--card-shadow);
            margin-bottom: 20px;
        }
        
        .page-title h1 {
            font-size: 26px;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 5px;
        }
        
        .page-title p {
            font-size: 15px;
            color: var(--text-light);
        }
        
        .settings-container {
            display: grid;
            grid-template-columns: 1fr;
            gap: 25px;
        }

        .settings-section {
            background-color: white;
            border-radius: var(--border-radius);
            padding: 30px;
            box-shadow: var(--card-shadow);
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-title::before {
            content: '';
            width: 4px;
            height: 24px;
            background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
            border-radius: 2px;
        }

        /* 环境检测样式 */
        .environment-check {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .software-item {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.9));
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 25px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .software-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .software-item:hover::before {
            opacity: 1;
        }

        .software-item.installed {
            border-color: var(--accent-green);
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(139, 195, 74, 0.05));
        }

        .software-item.not-installed {
            border-color: #f44336;
            background: linear-gradient(135deg, rgba(244, 67, 54, 0.1), rgba(239, 83, 80, 0.05));
        }

        .software-item.warning {
            border-color: #ff9800;
            background: linear-gradient(135deg, rgba(255, 152, 0, 0.1), rgba(255, 183, 77, 0.05));
        }

        .software-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .software-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .software-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .software-name {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-dark);
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-installed {
            background: linear-gradient(135deg, #4caf50, #66bb6a);
            color: white;
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
        }

        .status-not-installed {
            background: linear-gradient(135deg, #f44336, #ef5350);
            color: white;
            box-shadow: 0 2px 8px rgba(244, 67, 54, 0.3);
        }

        .status-checking {
            background: linear-gradient(135deg, #ff9800, #ffb74d);
            color: white;
            box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
            animation: pulse 2s infinite;
        }

        .status-success {
            background: linear-gradient(135deg, #4caf50, #66bb6a);
            color: white;
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
        }

        .status-warning {
            background: linear-gradient(135deg, #ff9800, #ffb74d);
            color: white;
            box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
        }

        .status-error {
            background: linear-gradient(135deg, #f44336, #ef5350);
            color: white;
            box-shadow: 0 2px 8px rgba(244, 67, 54, 0.3);
        }

        .status-unknown {
            background: linear-gradient(135deg, #9e9e9e, #bdbdbd);
            color: white;
            box-shadow: 0 2px 8px rgba(158, 158, 158, 0.3);
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .software-details {
            margin-bottom: 20px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            font-size: 14px;
        }

        .detail-row:last-child {
            border-bottom: none;
        }

        .detail-label {
            color: var(--text-light);
            font-weight: 500;
        }

        .detail-value {
            color: var(--text-dark);
            font-weight: 600;
            max-width: 200px;
            text-align: right;
            word-break: break-all;
        }

        .software-actions {
            display: flex;
            gap: 10px;
            position: relative;
            z-index: 10;
        }

        .action-button {
            flex: 1;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            position: relative;
            z-index: 20;
            pointer-events: auto !important;
        }

        .btn-download {
            background: linear-gradient(135deg, #2196f3, #42a5f5);
            color: white;
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
        }

        .btn-download:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
        }

        .btn-refresh {
            background: linear-gradient(135deg, #ff9800, #ffb74d);
            color: white;
            box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
        }

        .btn-refresh:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 152, 0, 0.4);
        }

        .btn-disabled {
            background: #e0e0e0 !important;
            color: #9e9e9e !important;
            cursor: not-allowed !important;
            box-shadow: none !important;
        }

        .btn-disabled:hover {
            transform: none !important;
            box-shadow: none !important;
        }

        .btn-disabled.truly-disabled {
            pointer-events: none !important;
        }

        .check-all-button {
            background: linear-gradient(135deg, var(--accent-purple), var(--accent-blue));
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(142, 68, 173, 0.3);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            min-height: 50px;
        }

        .check-all-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(142, 68, 173, 0.4);
        }

        .check-all-button:disabled {
            opacity: 0.8;
            cursor: not-allowed;
            transform: none;
        }

        .loading-spinner {
            width: 18px;
            height: 18px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-left-color: white;
            border-top-color: rgba(255, 255, 255, 0.8);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            flex-shrink: 0;
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* 检测状态动画 */
        .checking-animation {
            position: relative;
            overflow: hidden;
        }

        .checking-animation::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            animation: shimmer 1.5s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* 检测按钮状态样式 */
        .check-all-button.checking {
            background: linear-gradient(135deg, #6c757d, #5a6268);
            cursor: not-allowed;
        }

        .check-all-button.checking:hover {
            transform: none;
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
        }

        /* 成功状态动画 */
        @keyframes success-pulse {
            0% { box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3); }
            50% { box-shadow: 0 6px 25px rgba(76, 175, 80, 0.6); }
            100% { box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3); }
        }

        .check-all-button.success {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            animation: success-pulse 2s ease-in-out;
        }

        .logout-button {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .logout-button:hover {
            background: linear-gradient(135deg, #c0392b, #a93226);
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        .logout-button:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <!-- 导航菜单 -->
        <nav class="sidebar-nav">
            <a href="/" class="sidebar-icon" title="首页 - 浏览游戏列表">
                <div class="sidebar-icon-symbol">🏠</div>
                <div class="sidebar-icon-text">首页</div>
            </a>

            <a href="/管理" class="sidebar-icon" title="管理页面 - 游戏管理">
                <div class="sidebar-icon-symbol">📊</div>
                <div class="sidebar-icon-text">管理</div>
            </a>

            <a href="/附加功能" class="sidebar-icon" title="附加功能 - 扩展工具">
                <div class="sidebar-icon-symbol">🔧</div>
                <div class="sidebar-icon-text">附加功能</div>
            </a>

            <a href="/设置" class="sidebar-icon active" title="设置 - 系统配置">
                <div class="sidebar-icon-symbol">⚙️</div>
                <div class="sidebar-icon-text">设置</div>
            </a>

            <a href="https://wish.steamlab.cc/" class="sidebar-icon" title="许愿 - 游戏许愿" target="_blank">
                <div class="sidebar-icon-symbol">🌟</div>
                <div class="sidebar-icon-text">许愿</div>
            </a>

            <a href="https://help.steamlab.cc/%E4%B8%BB%E9%A1%B5.html" class="sidebar-icon" title="会员申请 - 申请会员服务" target="_blank">
                <div class="sidebar-icon-symbol">👑</div>
                <div class="sidebar-icon-text">会员申请</div>
            </a>

            <a href="https://help.steamlab.cc/%E5%94%AE%E5%90%8E%E5%B7%A5%E5%8D%95.html" class="sidebar-icon" title="售后工单 - 提交售后服务" target="_blank">
                <div class="sidebar-icon-symbol">🎫</div>
                <div class="sidebar-icon-text">售后工单</div>
            </a>
        </nav>
    </div>
    
    <div class="main-container">
        <div class="header">
            <div class="page-title">
                <h1>系统设置</h1>
                <p>您可以在这里配置 SteamVault Pro - Steam 宝库</p>
            </div>
        </div>
        
        <div class="settings-container">


            <!-- 运行环境检测区域 -->
            <div class="settings-section">
                <h2 class="section-title">
                    🔧 运行环境检测
                </h2>
                <p style="color: var(--text-light); margin-bottom: 20px;">
                    检查系统是否已安装必要的软件，确保游戏管理功能正常运行。
                </p>

                <button class="check-all-button" onclick="checkEnvironment()">
                    <span id="check-button-text">开始检测</span>
                    <div id="check-button-spinner" class="loading-spinner" style="display: none;"></div>
                </button>

                <div class="environment-check">
                    <!-- Steam检测 -->
                    <div class="software-item" id="steam-item">
                        <div class="software-header">
                            <div class="software-info">
                                <div class="software-icon">🎮</div>
                                <div class="software-name">Steam</div>
                            </div>
                            <div class="status-badge status-checking" id="steam-status">检测中...</div>
                        </div>

                        <div class="software-details" id="steam-details">
                            <div class="detail-row">
                                <span class="detail-label">安装状态:</span>
                                <span class="detail-value" id="steam-install-status">检测中...</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">安装路径:</span>
                                <span class="detail-value" id="steam-path">-</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">检测方法:</span>
                                <span class="detail-value" id="steam-method">-</span>
                            </div>
                        </div>

                        <div class="software-actions">
                            <button class="action-button btn-download btn-disabled truly-disabled" id="steam-download">
                                📁 打开安装包
                            </button>
                            <button class="action-button btn-refresh" id="steam-refresh">
                                🔄 重新检测
                            </button>
                        </div>
                    </div>

                    <!-- 7zip检测 -->
                    <div class="software-item" id="zip-item">
                        <div class="software-header">
                            <div class="software-info">
                                <div class="software-icon">📦</div>
                                <div class="software-name">7-Zip</div>
                            </div>
                            <div class="status-badge status-checking" id="zip-status">检测中...</div>
                        </div>

                        <div class="software-details" id="zip-details">
                            <div class="detail-row">
                                <span class="detail-label">安装状态:</span>
                                <span class="detail-value" id="zip-install-status">检测中...</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">安装路径:</span>
                                <span class="detail-value" id="zip-path">-</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">检测方法:</span>
                                <span class="detail-value" id="zip-method">-</span>
                            </div>
                        </div>

                        <div class="software-actions">
                            <button class="action-button btn-download btn-disabled truly-disabled" id="zip-download">
                                📁 打开安装包
                            </button>
                            <button class="action-button btn-refresh" id="zip-refresh">
                                🔄 重新检测
                            </button>
                        </div>
                    </div>

                    <!-- 配置检测 -->
                    <div class="software-item" id="config-item">
                        <div class="software-header">
                            <div class="software-info">
                                <div class="software-icon">⚙️</div>
                                <div class="software-name">配置环境</div>
                            </div>
                            <div class="status-badge status-unknown" id="config-status">未检测</div>
                        </div>

                        <div class="software-details" id="config-details">
                            <div class="detail-row">
                                <span class="detail-label">配置状态:</span>
                                <span class="detail-value" id="config-install-status">未检测</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">自动修复:</span>
                                <span class="detail-value" id="config-auto-fix">-</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">检测时间:</span>
                                <span class="detail-value" id="config-check-time">-</span>
                            </div>
                        </div>

                        <div class="software-actions">
                            <button class="action-button btn-refresh" id="config-check">
                                🔍 检测配置
                            </button>
                        </div>
                    </div>

                    <!-- Steam网络检测 -->
                    <div class="software-item" id="steam-network-item">
                        <div class="software-header">
                            <div class="software-info">
                                <div class="software-icon">🌐</div>
                                <div class="software-name">Steam网络</div>
                            </div>
                            <div class="status-badge status-unknown" id="steam-network-status">未检测</div>
                        </div>

                        <div class="software-details" id="steam-network-details">
                            <div class="detail-row">
                                <span class="detail-label">连接状态:</span>
                                <span class="detail-value" id="steam-network-connection">未检测</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">响应时间:</span>
                                <span class="detail-value" id="steam-network-response-time">-</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">状态码:</span>
                                <span class="detail-value" id="steam-network-status-code">-</span>
                            </div>
                        </div>

                        <div class="software-actions">
                            <button class="action-button btn-refresh" id="steam-network-check">
                                🔍 检测网络
                            </button>
                        </div>
                    </div>
                </div>

                <div id="environment-summary" style="margin-top: 25px; padding: 20px; border-radius: 10px; display: none;">
                    <h3 style="margin-bottom: 10px;">检测总结</h3>
                    <p id="summary-text"></p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 环境检测功能
        let isChecking = false;

        // 页面加载时自动检测
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，初始化功能...');

            // 初始化软件项目显示状态
            initializeSoftwareItems();

            // 手动绑定按钮事件（确保事件正确绑定）
            bindButtonEvents();

            setTimeout(checkEnvironment, 1000); // 延迟1秒自动检测

            // 添加测试按钮点击功能
            setTimeout(() => {
                addTestClickHandlers();
            }, 2000);
        });

        // 初始化软件项目显示状态
        function initializeSoftwareItems() {
            console.log('初始化软件项目显示状态...');

            // 确保所有软件项目都是可见的
            const softwareItems = ['steam-item', 'zip-item', 'config-item', 'network-item'];
            softwareItems.forEach(itemId => {
                const item = document.getElementById(itemId);
                if (item) {
                    item.style.display = 'block';
                    console.log(`显示项目: ${itemId}`);
                }
            });
        }

        // 手动绑定按钮事件
        function bindButtonEvents() {
            console.log('绑定按钮事件...');

            // 等待DOM完全加载
            setTimeout(() => {
                // 绑定Steam重新检测按钮
                const steamRefreshBtn = document.getElementById('steam-refresh');
                console.log('找到Steam按钮:', steamRefreshBtn);
                if (steamRefreshBtn) {
                    // 移除所有现有事件监听器
                    steamRefreshBtn.onclick = null;

                    // 添加新的事件监听器
                    steamRefreshBtn.onclick = function(e) {
                        console.log('Steam重新检测按钮被点击！');
                        e.preventDefault();
                        e.stopPropagation();
                        checkSteam();
                        return false;
                    };

                    // 确保按钮可以点击
                    steamRefreshBtn.style.pointerEvents = 'auto';
                    steamRefreshBtn.style.cursor = 'pointer';

                    console.log('Steam重新检测按钮事件已绑定');
                } else {
                    console.error('未找到Steam重新检测按钮');
                }

                // 绑定7zip重新检测按钮
                const zipRefreshBtn = document.getElementById('zip-refresh');
                console.log('找到7zip按钮:', zipRefreshBtn);
                if (zipRefreshBtn) {
                    // 移除所有现有事件监听器
                    zipRefreshBtn.onclick = null;

                    // 添加新的事件监听器
                    zipRefreshBtn.onclick = function(e) {
                        console.log('7zip重新检测按钮被点击！');
                        e.preventDefault();
                        e.stopPropagation();
                        check7zip();
                        return false;
                    };

                    // 确保按钮可以点击
                    zipRefreshBtn.style.pointerEvents = 'auto';
                    zipRefreshBtn.style.cursor = 'pointer';

                    console.log('7zip重新检测按钮事件已绑定');
                } else {
                    console.error('未找到7zip重新检测按钮');
                }

                // 绑定配置检测按钮
                const configCheckBtn = document.getElementById('config-check');
                console.log('找到配置检测按钮:', configCheckBtn);
                if (configCheckBtn) {
                    // 移除所有现有事件监听器
                    configCheckBtn.onclick = null;

                    // 添加新的事件监听器
                    configCheckBtn.onclick = function(e) {
                        console.log('配置检测按钮被点击！');
                        checkConfig();
                    };

                    // 确保按钮可以点击
                    configCheckBtn.style.pointerEvents = 'auto';
                    configCheckBtn.style.cursor = 'pointer';

                    console.log('配置检测按钮事件已绑定');
                } else {
                    console.error('未找到配置检测按钮');
                }

                // 绑定Steam网络检测按钮
                const steamNetworkCheckBtn = document.getElementById('steam-network-check');
                console.log('找到Steam网络检测按钮:', steamNetworkCheckBtn);
                if (steamNetworkCheckBtn) {
                    // 移除所有现有事件监听器
                    steamNetworkCheckBtn.onclick = null;

                    // 添加新的事件监听器
                    steamNetworkCheckBtn.onclick = function(e) {
                        console.log('Steam网络检测按钮被点击！');
                        checkSteamNetwork();
                    };

                    // 确保按钮可以点击
                    steamNetworkCheckBtn.style.pointerEvents = 'auto';
                    steamNetworkCheckBtn.style.cursor = 'pointer';

                    console.log('Steam网络检测按钮事件已绑定');
                } else {
                    console.error('未找到Steam网络检测按钮');
                }
            }, 100);
        }

        // 添加测试点击处理器
        function addTestClickHandlers() {
            console.log('添加测试点击处理器...');

            // 为所有按钮添加测试点击事件
            const allButtons = document.querySelectorAll('.action-button');
            allButtons.forEach((btn, index) => {
                console.log(`按钮 ${index}:`, btn, '类名:', btn.className);

                // 添加鼠标事件监听
                btn.addEventListener('mousedown', function(e) {
                    console.log(`按钮 ${index} 鼠标按下:`, this.textContent.trim());
                });

                btn.addEventListener('mouseup', function(e) {
                    console.log(`按钮 ${index} 鼠标释放:`, this.textContent.trim());
                });

                btn.addEventListener('click', function(e) {
                    console.log(`按钮 ${index} 被点击:`, this.textContent.trim());
                    // 只记录点击事件，不执行具体功能（避免重复执行）
                });
            });
        }



        // 检测所有环境
        async function checkEnvironment() {
            if (isChecking) return;

            isChecking = true;
            const button = document.querySelector('.check-all-button');
            const buttonText = document.getElementById('check-button-text');
            const buttonSpinner = document.getElementById('check-button-spinner');

            // 更新按钮状态
            buttonText.textContent = '检测中...';
            buttonSpinner.style.display = 'block';
            button.disabled = true;
            button.classList.add('checking', 'checking-animation');

            // 重置所有状态
            resetSoftwareStatus('steam');
            resetSoftwareStatus('zip');
            resetConfigStatus();
            resetSteamNetworkStatus();

            try {
                // 并行执行所有检测
                const [envResponse, networkResponse, configResponse] = await Promise.all([
                    fetch('/api/system/check-environment'),
                    fetch('/api/system/check-steam-network'),
                    fetch('/api/system/check-config', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    })
                ]);

                const envResult = await envResponse.json();
                const networkResult = await networkResponse.json();
                const configResult = await configResponse.json();

                // 更新环境检测结果
                if (envResult.status === 'success') {
                    updateSoftwareStatus('steam', envResult.steam);
                    updateSoftwareStatus('zip', envResult['7zip']);
                } else {
                    console.error('环境检测失败:', envResult.message);
                }

                // 更新Steam网络检测结果
                updateSteamNetworkStatus(networkResult);

                // 更新配置环境检测结果
                updateConfigStatus(configResult);

                // 更新环境总结，包含所有检测结果
                updateEnvironmentSummary(envResult, networkResult, configResult);

            } catch (error) {
                console.error('环境检测错误:', error);
                showError('环境检测失败: ' + error.message);
            } finally {
                isChecking = false;
                buttonText.textContent = '重新检测';
                buttonSpinner.style.display = 'none';
                button.disabled = false;
                button.classList.remove('checking', 'checking-animation');

                // 添加成功状态动画
                button.classList.add('success');
                setTimeout(() => {
                    button.classList.remove('success');
                }, 2000);
            }
        }

        // 重置软件状态
        function resetSoftwareStatus(software) {
            const item = document.getElementById(`${software}-item`);
            const status = document.getElementById(`${software}-status`);
            const installStatus = document.getElementById(`${software}-install-status`);
            const path = document.getElementById(`${software}-path`);
            const method = document.getElementById(`${software}-method`);
            const downloadBtn = document.getElementById(`${software}-download`);

            item.className = 'software-item';
            status.className = 'status-badge status-checking';
            status.textContent = '检测中...';
            installStatus.textContent = '检测中...';
            path.textContent = '-';
            method.textContent = '-';

            // 重置下载按钮状态
            downloadBtn.className = 'action-button btn-download btn-disabled';
            downloadBtn.textContent = '📁 检测中...';
            downloadBtn.onclick = null; // 清除点击事件
            downloadBtn.removeAttribute('disabled'); // 移除disabled属性
            downloadBtn.style.pointerEvents = 'none'; // 使用CSS禁用
        }

        // 更新软件状态
        function updateSoftwareStatus(software, data) {
            const item = document.getElementById(`${software}-item`);
            const status = document.getElementById(`${software}-status`);
            const installStatus = document.getElementById(`${software}-install-status`);
            const path = document.getElementById(`${software}-path`);
            const method = document.getElementById(`${software}-method`);
            const downloadBtn = document.getElementById(`${software}-download`);

            if (data.installed) {
                item.className = 'software-item installed';
                item.style.display = 'block'; // 显示已安装的软件
                status.className = 'status-badge status-installed';
                status.textContent = '已安装';
                installStatus.textContent = '✅ 已安装';
                path.textContent = data.path || '未知路径';
                method.textContent = getMethodText(data.method);

                // 已安装时禁用下载按钮
                downloadBtn.className = 'action-button btn-download btn-disabled truly-disabled';
                downloadBtn.textContent = '📁 已安装';
                downloadBtn.onclick = null;
            } else {
                // 未安装时显示未安装状态，但不隐藏项目
                item.className = 'software-item not-installed';
                item.style.display = 'block';
                status.className = 'status-badge status-not-installed';
                status.textContent = '未安装';
                installStatus.textContent = '❌ 未安装';
                path.textContent = '-';
                method.textContent = '-';

                // 未安装时启用下载按钮（如果有安装包）
                if (data.installer_available) {
                    downloadBtn.className = 'action-button btn-download';
                    downloadBtn.textContent = '📁 安装软件';
                    downloadBtn.onclick = () => openInstaller(software);
                } else {
                    downloadBtn.className = 'action-button btn-download btn-disabled';
                    downloadBtn.textContent = '📁 无安装包';
                    downloadBtn.onclick = null;
                }
            }
        }

        // 获取检测方法文本
        function getMethodText(method) {
            const methodMap = {
                'registry': '注册表检测',
                'common_path': '常见路径检测',
                'path': '环境变量检测',
                'command': '命令行检测'
            };
            return methodMap[method] || '未知方法';
        }

        // 更新环境总结
        function updateEnvironmentSummary(result) {
            const summary = document.getElementById('environment-summary');
            const summaryText = document.getElementById('summary-text');

            // 统计已安装和未安装的软件
            const installed = [];
            const notInstalled = [];

            if (result.steam.installed) {
                installed.push('Steam');
            } else {
                notInstalled.push('Steam');
            }

            if (result['7zip'].installed) {
                installed.push('7-Zip');
            } else {
                notInstalled.push('7-Zip');
            }

            // 总是显示检测结果
            summary.style.display = 'block';

            if (installed.length === 2) {
                // 全部安装
                summary.style.background = 'linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(139, 195, 74, 0.05))';
                summary.style.border = '2px solid var(--accent-green)';
                summaryText.innerHTML = `🎉 <strong>环境检测完成！</strong> 已检测到 ${installed.length} 个软件: ${installed.join(', ')}。系统运行环境良好。`;
            } else if (installed.length > 0) {
                // 部分安装
                summary.style.background = 'linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 235, 59, 0.05))';
                summary.style.border = '2px solid var(--accent-orange)';
                summaryText.innerHTML = `⚠️ <strong>环境检测完成！</strong> 已安装: ${installed.join(', ')}。未安装: ${notInstalled.join(', ')}。`;
            } else {
                // 全部未安装
                summary.style.background = 'linear-gradient(135deg, rgba(244, 67, 54, 0.1), rgba(255, 87, 34, 0.05))';
                summary.style.border = '2px solid var(--accent-red)';
                summaryText.innerHTML = `❌ <strong>环境检测完成！</strong> 未检测到任何必需软件。请安装: ${notInstalled.join(', ')}。`;
            }
        }

        // 单独检测Steam
        async function checkSteam() {
            console.log('开始检测Steam...');
            const button = document.getElementById('steam-refresh');

            // 添加加载状态
            if (button) {
                button.innerHTML = '🔄 检测中...';
                button.disabled = true;
                button.classList.add('checking', 'checking-animation');
            }

            resetSoftwareStatus('steam');
            try {
                const response = await fetch('/api/system/check-environment');
                const result = await response.json();
                if (result.status === 'success') {
                    updateSoftwareStatus('steam', result.steam);
                    showNotification('Steam检测完成', 'success');
                } else {
                    showError('Steam检测失败: ' + result.message);
                }
            } catch (error) {
                console.error('Steam检测错误:', error);
                showError('Steam检测失败: ' + error.message);
            } finally {
                // 恢复按钮状态
                const button = document.getElementById('steam-refresh');
                if (button) {
                    button.innerHTML = '🔄 重新检测';
                    button.disabled = false;
                    button.classList.remove('checking', 'checking-animation');
                    button.classList.add('success');
                    setTimeout(() => {
                        button.classList.remove('success');
                    }, 2000);
                }
            }
        }

        // 单独检测7zip
        async function check7zip() {
            console.log('开始检测7-Zip...');
            const button = document.getElementById('zip-refresh');

            // 添加加载状态
            if (button) {
                button.innerHTML = '🔄 检测中...';
                button.disabled = true;
                button.classList.add('checking', 'checking-animation');
            }

            resetSoftwareStatus('zip');
            try {
                const response = await fetch('/api/system/check-environment');
                const result = await response.json();
                if (result.status === 'success') {
                    updateSoftwareStatus('zip', result['7zip']);
                    showNotification('7-Zip检测完成', 'success');
                } else {
                    showError('7-Zip检测失败: ' + result.message);
                }
            } catch (error) {
                console.error('7-Zip检测错误:', error);
                showError('7-Zip检测失败: ' + error.message);
            } finally {
                // 恢复按钮状态
                const button = document.getElementById('zip-refresh');
                if (button) {
                    button.innerHTML = '🔄 重新检测';
                    button.disabled = false;
                    button.classList.remove('checking', 'checking-animation');
                    button.classList.add('success');
                    setTimeout(() => {
                        button.classList.remove('success');
                    }, 2000);
                }
            }
        }

        // 配置环境检测
        async function checkConfig() {
            console.log('开始检测配置环境...');
            const button = document.getElementById('config-check');

            // 添加加载状态
            if (button) {
                button.innerHTML = '🔄 检测中...';
                button.disabled = true;
                button.classList.add('checking', 'checking-animation');
            }

            resetConfigStatus();

            try {
                const response = await fetch('/api/system/check-config', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                const result = await response.json();

                updateConfigStatus(result);

                if (result.status === 'success') {
                    showNotification('配置环境检测完成', 'success');
                } else if (result.status === 'warning') {
                    showNotification('配置环境部分修复', 'warning');
                } else {
                    showNotification('配置环境检测发现问题', 'warning');
                }
            } catch (error) {
                console.error('配置检测错误:', error);
                showNotification('配置检测失败', 'error');
                updateConfigStatus({
                    status: 'error',
                    message: '检测失败',
                    auto_fixed: false,
                    details: { missing_count: 0, steam_path_found: false }
                });
            } finally {
                // 恢复按钮状态
                const button = document.getElementById('config-check');
                if (button) {
                    button.innerHTML = '🔍 检测配置';
                    button.disabled = false;
                    button.classList.remove('checking', 'checking-animation');
                    button.classList.add('success');
                    setTimeout(() => {
                        button.classList.remove('success');
                    }, 2000);
                }
            }
        }

        // Steam网络连接检测
        async function checkSteamNetwork() {
            console.log('开始检测Steam网络连接...');
            const button = document.getElementById('steam-network-check');

            // 添加加载状态
            if (button) {
                button.innerHTML = '🔄 检测中...';
                button.disabled = true;
                button.classList.add('checking', 'checking-animation');
            }

            resetSteamNetworkStatus();

            try {
                const response = await fetch('/api/system/check-steam-network');
                const result = await response.json();

                updateSteamNetworkStatus(result);

                if (result.status === 'success' && result.connected) {
                    showNotification('Steam网络连接正常', 'success');
                } else if (result.status === 'warning') {
                    showNotification('Steam网络连接异常', 'warning');
                } else {
                    showNotification('Steam网络连接失败', 'error');
                }
            } catch (error) {
                console.error('Steam网络检测错误:', error);
                showNotification('Steam网络检测失败', 'error');
                updateSteamNetworkStatus({
                    status: 'error',
                    connected: false,
                    response_time: 0,
                    status_code: 0,
                    message: '检测失败'
                });
            } finally {
                // 恢复按钮状态
                const button = document.getElementById('steam-network-check');
                if (button) {
                    button.innerHTML = '🔍 检测网络';
                    button.disabled = false;
                    button.classList.remove('checking', 'checking-animation');
                    button.classList.add('success');
                    setTimeout(() => {
                        button.classList.remove('success');
                    }, 2000);
                }
            }
        }

        // 重置配置状态
        function resetConfigStatus() {
            const item = document.getElementById('config-item');
            item.className = 'software-item checking';
            document.getElementById('config-status').textContent = '检测中...';
            document.getElementById('config-status').className = 'status-badge status-checking';
            document.getElementById('config-install-status').textContent = '检测中...';
            document.getElementById('config-auto-fix').textContent = '-';
            document.getElementById('config-check-time').textContent = '-';
        }

        // 更新配置状态
        function updateConfigStatus(result) {
            const item = document.getElementById('config-item');
            const statusBadge = document.getElementById('config-status');
            const installStatus = document.getElementById('config-install-status');
            const autoFix = document.getElementById('config-auto-fix');
            const checkTime = document.getElementById('config-check-time');

            // 更新状态徽章和软件项目样式
            if (result.status === 'success') {
                item.className = 'software-item installed';
                statusBadge.textContent = '正常';
                statusBadge.className = 'status-badge status-installed';
                installStatus.textContent = '✅ ' + result.message;
            } else if (result.status === 'warning') {
                item.className = 'software-item warning';
                statusBadge.textContent = '部分修复';
                statusBadge.className = 'status-badge status-warning';
                installStatus.textContent = '⚠️ ' + result.message;
            } else {
                item.className = 'software-item not-installed';
                statusBadge.textContent = '异常';
                statusBadge.className = 'status-badge status-not-installed';
                installStatus.textContent = '❌ ' + result.message;
            }

            // 更新自动修复状态
            autoFix.textContent = result.auto_fixed ? '✅ 已执行' : '❌ 未执行';

            // 更新检测时间
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-CN', { hour12: false });
            checkTime.textContent = timeString;
        }

        // 重置Steam网络状态
        function resetSteamNetworkStatus() {
            const item = document.getElementById('steam-network-item');
            const status = document.getElementById('steam-network-status');
            const connection = document.getElementById('steam-network-connection');
            const responseTime = document.getElementById('steam-network-response-time');
            const statusCode = document.getElementById('steam-network-status-code');

            item.className = 'software-item checking';
            status.className = 'status-badge status-checking';
            status.textContent = '检测中...';
            connection.textContent = '检测中...';
            responseTime.textContent = '-';
            statusCode.textContent = '-';
        }

        // 更新Steam网络状态
        function updateSteamNetworkStatus(result) {
            const item = document.getElementById('steam-network-item');
            const statusBadge = document.getElementById('steam-network-status');
            const connection = document.getElementById('steam-network-connection');
            const responseTime = document.getElementById('steam-network-response-time');
            const statusCode = document.getElementById('steam-network-status-code');

            // 更新状态徽章和软件项目样式
            if (result.status === 'success' && result.connected) {
                item.className = 'software-item installed';
                statusBadge.textContent = '连接正常';
                statusBadge.className = 'status-badge status-success';
                connection.textContent = '✅ ' + result.message;
            } else if (result.status === 'warning') {
                item.className = 'software-item warning';
                statusBadge.textContent = '连接异常';
                statusBadge.className = 'status-badge status-warning';
                connection.textContent = '⚠️ ' + result.message;
            } else {
                item.className = 'software-item not-installed';
                statusBadge.textContent = '连接失败';
                statusBadge.className = 'status-badge status-error';
                connection.textContent = '❌ ' + result.message;
            }

            // 更新响应时间和状态码
            if (result.response_time > 0) {
                responseTime.textContent = `${(result.response_time * 1000).toFixed(0)}ms`;
            } else {
                responseTime.textContent = '-';
            }

            if (result.status_code > 0) {
                statusCode.textContent = result.status_code;
            } else {
                statusCode.textContent = '-';
            }
        }

        // 打开安装包
        async function openInstaller(software) {
            try {
                const softwareName = software === 'steam' ? 'Steam' : '7-Zip';
                showNotification(`正在启动 ${softwareName} 安装包...`, 'info');

                const response = await fetch(`/api/system/run-installer/${software}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const result = await response.json();

                if (result.status === 'success') {
                    showNotification(result.message, 'success');
                } else {
                    showNotification(`启动失败: ${result.message}`, 'error');
                }
            } catch (error) {
                console.error('启动安装包失败:', error);
                showNotification('启动安装包失败，请手动运行安装包', 'error');
            }
        }

        // 显示错误信息
        function showError(message) {
            const summary = document.getElementById('environment-summary');
            const summaryText = document.getElementById('summary-text');

            summary.style.display = 'block';
            summary.style.background = 'linear-gradient(135deg, rgba(244, 67, 54, 0.1), rgba(239, 83, 80, 0.05))';
            summary.style.border = '2px solid #f44336';
            summaryText.innerHTML = `❌ <strong>检测失败！</strong> ${message}`;
        }

        // 通知系统（复用首页的通知功能）
        function showNotification(message, type = 'success') {
            // 创建通知容器（如果不存在）
            let toastContainer = document.getElementById('toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toast-container';
                toastContainer.style.cssText = `
                    position: fixed;
                    top: 25px;
                    right: 25px;
                    z-index: 9999;
                    pointer-events: none;
                    max-width: 400px;
                `;
                document.body.appendChild(toastContainer);
            }

            const toast = document.createElement('div');
            toast.style.cssText = `
                background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 249, 250, 0.95));
                backdrop-filter: blur(20px);
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 16px;
                padding: 16px 20px;
                margin-bottom: 12px;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 8px 25px rgba(0, 0, 0, 0.08);
                display: flex;
                align-items: center;
                gap: 12px;
                font-size: 15px;
                font-weight: 500;
                max-width: 380px;
                min-width: 280px;
                opacity: 0;
                transform: translateX(100px) scale(0.8);
                transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
                pointer-events: auto;
                cursor: pointer;
            `;

            let iconContent = '✓';
            let iconColor = '#4caf50';
            if (type === 'error') {
                iconContent = '✕';
                iconColor = '#f44336';
            } else if (type === 'info') {
                iconContent = 'ℹ';
                iconColor = '#2196f3';
            } else if (type === 'warning') {
                iconContent = '⚠';
                iconColor = '#ff9800';
            }

            toast.innerHTML = `
                <div style="
                    width: 32px;
                    height: 32px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 18px;
                    font-weight: bold;
                    color: white;
                    background: ${iconColor};
                    box-shadow: 0 4px 15px ${iconColor}30;
                ">${iconContent}</div>
                <div style="flex: 1; line-height: 1.4; color: #2d3748;">${message}</div>
            `;

            toastContainer.appendChild(toast);

            // 显示动画
            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateX(0) scale(1)';
            }, 50);

            // 点击关闭
            toast.addEventListener('click', () => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100px) scale(0.8)';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.remove();
                    }
                }, 400);
            });

            // 自动关闭
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.style.opacity = '0';
                    toast.style.transform = 'translateX(100px) scale(0.8)';
                    setTimeout(() => {
                        if (toast.parentNode) {
                            toast.remove();
                        }
                    }, 400);
                }
            }, 3000);
        }


    </script>
</body>
</html>