#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
路径工具模块
提供统一的路径获取功能，兼容PyInstaller打包环境
"""

import os
import sys


def 获取应用程序路径():
    """
    获取应用程序所在目录的路径（兼容PyInstaller打包）
    
    Returns:
        str: 应用程序路径
    """
    if getattr(sys, 'frozen', False):
        # 如果是PyInstaller打包的exe，使用临时目录
        # PyInstaller会将资源文件解压到sys._MEIPASS目录
        if hasattr(sys, '_MEIPASS'):
            application_path = sys._MEIPASS
        else:
            # 备用方案：使用exe文件所在目录
            application_path = os.path.dirname(sys.executable)
    else:
        # 如果是普通Python脚本，获取项目根目录
        # 当前文件在 模块/路径工具.py，所以需要向上两级到达项目根目录
        current_file = os.path.abspath(__file__)
        module_dir = os.path.dirname(current_file)  # 模块目录
        application_path = os.path.dirname(module_dir)  # 项目根目录
    return application_path


def 获取配置文件路径():
    """
    获取配置文件.zip的完整路径
    
    Returns:
        str: 配置文件路径
    """
    app_path = 获取应用程序路径()
    return os.path.join(app_path, "配置文件.zip")


def 获取安装包目录路径():
    """
    获取安装包文件目录的完整路径
    
    Returns:
        str: 安装包目录路径
    """
    app_path = 获取应用程序路径()
    return os.path.join(app_path, "安装包文件")


def 获取Steam安装包路径():
    """
    获取Steam安装包的完整路径
    
    Returns:
        str: Steam安装包路径
    """
    installer_dir = 获取安装包目录路径()
    return os.path.join(installer_dir, "Steam安装包.exe")


def 获取7zip安装包路径():
    """
    获取7zip安装包的完整路径
    
    Returns:
        str: 7zip安装包路径
    """
    installer_dir = 获取安装包目录路径()
    return os.path.join(installer_dir, "7zip安装包.exe")


def 获取Geek卸载器路径():
    """
    获取Geek Uninstaller的完整路径
    
    Returns:
        str: Geek Uninstaller路径
    """
    installer_dir = 获取安装包目录路径()
    return os.path.join(installer_dir, "Geek Uninstaller.exe")


def 检查路径是否存在(路径):
    """
    检查指定路径是否存在
    
    Args:
        路径 (str): 要检查的路径
        
    Returns:
        bool: 路径是否存在
    """
    return os.path.exists(路径)


def 获取路径信息():
    """
    获取所有重要路径的信息（用于调试）
    
    Returns:
        dict: 路径信息字典
    """
    info = {
        "应用程序路径": 获取应用程序路径(),
        "配置文件路径": 获取配置文件路径(),
        "安装包目录路径": 获取安装包目录路径(),
        "Steam安装包路径": 获取Steam安装包路径(),
        "7zip安装包路径": 获取7zip安装包路径(),
        "Geek卸载器路径": 获取Geek卸载器路径(),
        "是否PyInstaller环境": getattr(sys, 'frozen', False),
        "sys._MEIPASS": getattr(sys, '_MEIPASS', '不存在'),
        "sys.executable": sys.executable,
        "当前工作目录": os.getcwd()
    }
    
    # 添加存在性检查
    path_keys = [key for key in info.keys() if key.endswith("路径")]
    for key in path_keys:
        path = info[key]
        if isinstance(path, str):
            info[f"{key}_存在"] = 检查路径是否存在(path)
    
    return info
