# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['c:/Users/<USER>/Desktop/Python代码项目/入库工具网页版 - 副本/main.py'],
    pathex=['c:/Users/<USER>/Desktop/Python代码项目/入库工具网页版 - 副本'],
    binaries=[],
    datas=[('templates', 'templates'), ('static', 'static'), ('安装包文件', '安装包文件'), ('配置文件.zip', '.'), ('登录配置.json', '.'), ('登录窗口.py', '.')],
    hiddenimports=['flask', 'requests', 'patoolib', 'winreg', 'subprocess', 'threading', 'tempfile', 'shutil', 'urllib.request', 'urllib.parse', 'json', 'logging', 'time', 'os', 'sys', 'io', 'pathlib', 'tkinter', 'tkinter.messagebox', 'customtkinter', 'Crypto.Cipher.DES', 'Crypto.Util.Padding', 'platform', 'uuid', 'hashlib', 'base64', 'typing', 'webbrowser', 'zipfile', 'psutil', 're', 'atexit', '模块.配置', '模块.日志', '模块.网页显示', '模块.网络请求', '模块.数据处理', '模块.VDF处理', '模块.配置检测', '模块.Steam功能', '模块.路径工具', '模块.网络验证'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['matplotlib', 'numpy', 'pandas', 'scipy', 'PIL', 'cv2', 'tensorflow', 'torch', 'jupyter', 'IPython', 'pytest', 'unittest', 'doctest'],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='SteamVault Pro - Steam 宝库',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='c:/Users/<USER>/Desktop/Python代码项目/入库工具网页版 - 副本/static/icons8-游戏文件夹-50_64x64.ico',
    version_file=None,
)
