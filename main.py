from flask import Flask, render_template_string, send_from_directory
from 模块.配置 import PORT
from 模块.日志 import logger, send_remote_log
from 模块.网页显示 import web_bp
from 模块.配置检测 import 首次启动检测
import os
import logging
import threading
import sys
import webbrowser
import time
import atexit

# 处理PyInstaller打包后的路径问题
def get_resource_path(relative_path):
    """获取资源文件的绝对路径，兼容开发环境和打包后的环境"""
    try:
        # PyInstaller创建临时文件夹，并将路径存储在_MEIPASS中
        base_path = sys._MEIPASS
    except AttributeError:
        # 开发环境中使用当前文件的目录
        base_path = os.path.dirname(os.path.abspath(__file__))

    return os.path.join(base_path, relative_path)

# 设置Flask应用的模板和静态文件路径
template_folder = get_resource_path('templates')
static_folder = get_resource_path('static')

app = Flask(__name__,
           template_folder=template_folder,
           static_folder=static_folder)
app.register_blueprint(web_bp)

# 设置Flask日志级别，隐藏HTTP请求日志
logging.getLogger('werkzeug').setLevel(logging.ERROR)

# 首次启动配置检测标志
_first_run_check_done = False

# 全局网络验证客户端实例
auth_client = None

class AuthClientManager:
    """网络验证客户端管理器"""
    _instance = None

    def __init__(self):
        self.auth_client = None

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def set_client(self, client):
        self.auth_client = client

    def get_client(self):
        return self.auth_client

# 创建全局管理器实例
auth_manager = AuthClientManager.get_instance()

def 执行首次配置检测():
    """在后台线程中执行首次配置检测"""
    global _first_run_check_done
    if not _first_run_check_done:
        try:
            # 完全静默的配置检测
            首次启动检测()
            _first_run_check_done = True
        except:
            # 静默失败，不记录任何日志
            pass

def 自动打开浏览器():
    """等待服务启动后自动打开浏览器"""
    time.sleep(3)  # 等待3秒确保服务启动
    try:
        url = f"http://127.0.0.1:{PORT}/"
        print(f"正在打开浏览器: {url}")
        webbrowser.open(url)
    except Exception as e:
        print(f"打开浏览器失败: {e}")
        print(f"请手动访问: http://127.0.0.1:{PORT}/")

@app.route('/favicon.ico')
def favicon():
    """提供favicon图标"""
    return send_from_directory(get_resource_path('static'), 'favicon.ico', mimetype='image/x-icon')

@app.errorhandler(404)
def page_not_found(_):
    return render_template_string(ERROR_HTML, error="页面未找到"), 404

@app.errorhandler(500)
def internal_server_error(_):
    return render_template_string(ERROR_HTML, error="服务器内部错误"), 500

# HTML模板
INDEX_HTML = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SteamVault Pro - Steam 宝库</title>
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
    <link rel="icon" type="image/png" href="/static/favicon.png">
    <link rel="shortcut icon" href="/static/favicon.ico">
    <link rel="stylesheet" href="/static/css/特效样式.css">
    <script src="/static/js/安全防护.js"></script>
    <script src="/static/js/特效管理器.js"></script>
    <script src="/static/js/通知系统.js"></script>
    <!-- 动画库 - 使用中国大陆可访问的CDN -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/animate.css/4.1.1/animate.min.css">
    <!-- AOS动画库 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/aos/2.3.4/aos.css">
    <style>
        :root {
            --primary-bg: #656f8c;
            --sidebar-bg: #373e54;
            --card-bg: #eef1f8;
            --card-purple: #f7eeff;
            --card-blue: #edf5ff;
            --text-dark: #333;
            --text-light: #666;
            --accent-green: #4CAF50;
            --accent-purple: #8e44ad;
            --accent-blue: #3498db;
            --border-radius: 12px;
            --card-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background-color: var(--primary-bg);
            color: var(--text-dark);
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            background: linear-gradient(135deg, var(--sidebar-bg) 0%, #2c3e50 100%);
            width: 85px;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 25px 0;
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.15);
            position: relative;
            overflow: hidden;
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            pointer-events: none;
        }

        .sidebar-nav {
            display: flex;
            flex-direction: column;
            gap: 12px;
            width: 100%;
            align-items: center;
            padding-top: 10px;
        }

        .sidebar-icon {
            width: 55px;
            height: 55px;
            border-radius: 18px;
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            margin-bottom: 5px;
        }

        .sidebar-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
        }

        .sidebar-icon:hover::before {
            left: 100%;
        }

        .sidebar-icon-symbol {
            font-size: 22px;
            margin-bottom: 2px;
            transition: all 0.3s ease;
        }

        .sidebar-icon-text {
            font-size: 10px;
            font-weight: 500;
            opacity: 0.9;
            transition: all 0.3s ease;
        }

        .sidebar-icon:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.2);
            color: white;
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .sidebar-icon:hover .sidebar-icon-symbol {
            transform: scale(1.1);
        }

        .sidebar-icon.active {
            background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
            border-color: var(--accent-blue);
            color: white;
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
            transform: translateY(-1px);
        }

        .sidebar-icon.active::after {
            content: '';
            position: absolute;
            right: -3px;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 30px;
            background: linear-gradient(to bottom, var(--accent-blue), var(--accent-purple));
            border-radius: 2px;
            box-shadow: 0 0 10px rgba(52, 152, 219, 0.5);
        }

        .sidebar-icon.active .sidebar-icon-symbol {
            transform: scale(1.1);
        }


        
        .main-container {
            flex: 1;
            padding: 30px;
            display: flex;
            flex-direction: column;
            overflow-x: hidden;
        }
        
        .header {
            display: flex;
            flex-direction: column;
            margin-bottom: 30px;
        }
        
        .greeting-card {
            background-color: var(--card-blue);
            border-radius: var(--border-radius);
            padding: 25px;
            box-shadow: var(--card-shadow);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .greeting-text h1 {
            font-size: 26px;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 5px;
        }
        
        .greeting-text p {
            font-size: 15px;
            color: var(--text-light);
        }
        
        .greeting-stats {
            background-color: white;
            border-radius: 10px;
            padding: 12px 25px;
            text-align: center;
            box-shadow: var(--card-shadow);
        }
        
        .greeting-stats span {
            font-size: 28px;
            font-weight: 700;
            color: var(--accent-blue);
            display: block;
            margin-bottom: 2px;
        }
        
        .greeting-stats small {
            color: var(--text-light);
            font-size: 13px;
        }
        
        .search-wrapper {
            background-color: white;
            border-radius: var(--border-radius);
            padding: 20px;
            box-shadow: var(--card-shadow);
        }
        
        .search-container {
            display: flex;
            flex-direction: column;
            gap: 15px;
            width: 100%;
        }
        
        .search-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-dark);
        }
        
        .search-box {
            display: flex;
            align-items: center;
            background-color: var(--card-bg);
            border-radius: 10px;
            padding: 12px 20px;
            transition: box-shadow 0.3s;
            border: 2px solid transparent;
        }
        
        .search-box:focus-within {
            border-color: var(--accent-blue);
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
        }
        
        .search-icon {
            font-size: 20px;
            color: var(--accent-blue);
            margin-right: 15px;
        }
        
        .search-box input {
            flex: 1;
            border: none;
            outline: none;
            background: transparent;
            font-size: 16px;
            padding: 0;
        }
        
        .search-box input::placeholder {
            color: #aaa;
        }
        
        .search-options {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }
        
        .search-option {
            padding: 5px 15px;
            background-color: white;
            border: 1px solid #e0e0e0;
            border-radius: 15px;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .search-option:hover, .search-option.active {
            background-color: var(--accent-blue);
            color: white;
            border-color: var(--accent-blue);
        }
        
        .games-container {
            background-color: white;
            border-radius: var(--border-radius);
            padding: 20px;
            box-shadow: var(--card-shadow);
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            margin-top: 20px;
        }
        
        .container-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .container-title {
            font-size: 18px;
            font-weight: 600;
        }
        
        .all-button {
            padding: 8px 20px;
            background-color: var(--accent-blue);
            color: white;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .all-button:hover {
            background-color: #2980b9;
        }
        
        .games-list {
            flex: 1;
            overflow-y: auto;
            border-radius: 8px;
        }
        
        .game-card {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            transition: background-color 0.2s;
        }
        
        .game-card:hover {
            background-color: rgba(0, 0, 0, 0.02);
        }
        
        .game-info {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .game-name {
            font-weight: 600;
            font-size: 16px;
        }
        
        .game-details {
            display: flex;
            gap: 15px;
            font-size: 14px;
            color: var(--text-light);
        }
        
        .game-tag {
            padding: 3px 10px;
            background-color: var(--card-bg);
            border-radius: 20px;
            font-size: 12px;
        }
        
        .game-type {
            color: var(--accent-purple);
        }
        
        .game-time {
            color: var(--text-light);
            font-size: 14px;
        }
        
        /* 游戏卡片按钮样式 */
        .game-buttons {
            display: flex;
            gap: 8px;
            margin-top: 10px;
        }

        .game-button {
            padding: 8px 14px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: none;
            display: flex;
            align-items: center;
            gap: 6px;
            color: white;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
            letter-spacing: 0.5px;
            min-width: 90px;
            justify-content: center;
        }

        .game-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.6s ease-out;
        }

        .game-button::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.3s, height 0.3s;
        }

        .game-button:hover::before {
            left: 100%;
        }

        .game-button:hover::after {
            width: 120%;
            height: 120%;
        }

        .game-button:active {
            transform: translateY(1px) scale(0.96);
        }

        .game-button:hover {
            letter-spacing: 1px;
        }

        .button-install {
            background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 25%, #4CAF50 50%, #45a049 75%, #3d8b40 100%);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .button-install:hover {
            background: linear-gradient(135deg, #66BB6A 0%, #81C784 25%, #5cbf60 50%, #4CAF50 75%, #45a049 100%);
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 10px 30px rgba(76, 175, 80, 0.5), inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .button-download {
            background: linear-gradient(135deg, #FF6B35 0%, #FF8A50 25%, #e67e22 50%, #E67E22 75%, #d35400 100%);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 4px 15px rgba(230, 126, 34, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .button-download:hover {
            background: linear-gradient(135deg, #FF8A50 0%, #FFB74D 25%, #ff7f50 50%, #ff6b35 75%, #e67e22 100%);
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 10px 30px rgba(230, 126, 34, 0.5), inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .button-store {
            background: linear-gradient(135deg, #3498DB 0%, #5DADE2 25%, #2980b9 50%, #2471A3 75%, #1f5f8b 100%);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .button-store:hover {
            background: linear-gradient(135deg, #5DADE2 0%, #85C1E9 25%, #5dade2 50%, #3498db 75%, #2980b9 100%);
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 10px 30px rgba(52, 152, 219, 0.5), inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .button-db {
            background: linear-gradient(135deg, #9B59B6 0%, #BB8FCE 25%, #8e44ad 50%, #7D3C98 75%, #6C3483 100%);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 4px 15px rgba(142, 68, 173, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .button-db:hover {
            background: linear-gradient(135deg, #BB8FCE 0%, #D7BDE2 25%, #bb8fce 50%, #9b59b6 75%, #8e44ad 100%);
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 10px 30px rgba(142, 68, 173, 0.5), inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .button-details {
            background: linear-gradient(135deg, #F39C12 0%, #F7DC6F 25%, #e67e22 50%, #D68910 75%, #d35400 100%);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .button-details:hover {
            background: linear-gradient(135deg, #F7DC6F 0%, #FCF3CF 25%, #f7dc6f 50%, #f39c12 75%, #e67e22 100%);
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 10px 30px rgba(243, 156, 18, 0.5), inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .button-report {
            background: linear-gradient(135deg, #E74C3C 0%, #F1948A 25%, #e74c3c 50%, #C0392B 75%, #A93226 100%);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .button-report:hover {
            background: linear-gradient(135deg, #F1948A 0%, #FADBD8 25%, #f1948a 50%, #e74c3c 75%, #C0392B 100%);
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 10px 30px rgba(231, 76, 60, 0.5), inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        /* 报告错误模态框样式 */
        .report-modal-content {
            max-width: 500px;
            width: 90%;
        }

        /* 批量入库确认对话框样式 */
        .batch-install-modal-content {
            max-width: 600px;
            width: 90%;
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 20px;
            overflow: hidden;
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.25),
                0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.6);
        }

        .batch-install-info {
            padding: 0;
        }

        .feature-highlights,
        .usage-guide,
        .warm-tips {
            margin-bottom: 25px;
            padding: 20px;
            border-radius: 15px;
            position: relative;
            overflow: hidden;
        }

        .feature-highlights {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border: 2px solid rgba(102, 126, 234, 0.2);
        }

        .usage-guide {
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(56, 142, 60, 0.1));
            border: 2px solid rgba(76, 175, 80, 0.2);
        }

        .warm-tips {
            background: linear-gradient(135deg, rgba(255, 152, 0, 0.1), rgba(245, 124, 0, 0.1));
            border: 2px solid rgba(255, 152, 0, 0.2);
        }

        .feature-highlights::before,
        .usage-guide::before,
        .warm-tips::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }

        .batch-install-info h3 {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 15px;
            color: #2d3748;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .feature-list,
        .usage-list,
        .tips-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .feature-list li,
        .usage-list li,
        .tips-list li {
            padding: 8px 0;
            font-size: 15px;
            color: #4a5568;
            line-height: 1.6;
            position: relative;
            padding-left: 20px;
        }

        .feature-list li::before {
            content: '✨';
            position: absolute;
            left: 0;
            top: 8px;
        }

        .usage-list li::before {
            content: '🔧';
            position: absolute;
            left: 0;
            top: 8px;
        }

        .tips-list li::before {
            content: '💡';
            position: absolute;
            left: 0;
            top: 8px;
        }

        .tips-content p {
            margin: 10px 0;
            color: #4a5568;
            font-size: 15px;
            line-height: 1.6;
        }

        .advanced-tip {
            background: linear-gradient(135deg, rgba(156, 39, 176, 0.1), rgba(123, 31, 162, 0.1));
            padding: 12px 15px;
            border-radius: 10px;
            border-left: 4px solid #9c27b0;
            font-style: italic;
            color: #6a1b9a !important;
            margin-top: 15px !important;
        }

        /* 动态通知系统样式 */
        .dynamic-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            min-width: 300px;
            max-width: 400px;
            padding: 16px 20px;
            border-radius: 12px;
            color: white;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .dynamic-notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        .dynamic-notification.success {
            background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
            border-left: 4px solid #2E7D32;
        }

        .dynamic-notification.error {
            background: linear-gradient(135deg, #F44336 0%, #EF5350 100%);
            border-left: 4px solid #C62828;
        }

        .dynamic-notification.warning {
            background: linear-gradient(135deg, #FF9800 0%, #FFB74D 100%);
            border-left: 4px solid #F57C00;
        }

        .dynamic-notification.info {
            background: linear-gradient(135deg, #2196F3 0%, #42A5F5 100%);
            border-left: 4px solid #1565C0;
        }

        .notification-icon {
            font-size: 20px;
            flex-shrink: 0;
        }

        .notification-content {
            flex: 1;
            line-height: 1.4;
        }

        .notification-close {
            font-size: 18px;
            cursor: pointer;
            opacity: 0.7;
            transition: opacity 0.2s;
            flex-shrink: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
        }

        .notification-close:hover {
            opacity: 1;
            background: rgba(255, 255, 255, 0.2);
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .dynamic-notification {
                top: 10px;
                right: 10px;
                left: 10px;
                min-width: auto;
                max-width: none;
            }
        }

        .report-game-info {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .report-game-info h3 {
            margin: 0 0 8px 0;
            color: var(--primary-color);
            font-size: 18px;
            font-weight: 600;
        }

        .report-game-info p {
            margin: 0;
            color: var(--text-light);
            font-size: 14px;
        }

        .error-types {
            margin-bottom: 30px;
        }

        .error-types h4 {
            margin: 0 0 15px 0;
            color: var(--text-color);
            font-size: 16px;
            font-weight: 600;
        }

        .error-type-options {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .error-type-option {
            display: flex;
            align-items: center;
            padding: 15px;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border: 2px solid #e9ecef;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .error-type-option::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(52, 152, 219, 0.1), transparent);
            transition: left 0.6s ease;
        }

        .error-type-option:hover {
            border-color: var(--accent-blue);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.15);
        }

        .error-type-option:hover::before {
            left: 100%;
        }

        .error-type-option input[type="radio"] {
            margin-right: 12px;
            width: 18px;
            height: 18px;
            accent-color: var(--accent-blue);
        }

        .error-type-option input[type="radio"]:checked + .error-type-text {
            color: var(--accent-blue);
            font-weight: 600;
        }

        .error-type-option:has(input[type="radio"]:checked) {
            border-color: var(--accent-blue);
            background: linear-gradient(135deg, #e3f2fd 0%, #f0f8ff 100%);
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.2);
        }

        .error-type-text {
            font-size: 14px;
            color: var(--text-color);
            transition: all 0.3s ease;
        }

        .modal-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }

        .modal-button {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            min-width: 100px;
        }

        .modal-button::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.3s, height 0.3s;
        }

        .modal-button:hover::before {
            width: 120%;
            height: 120%;
        }

        .modal-button-cancel {
            background: linear-gradient(135deg, #6c757d 0%, #868e96 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
        }

        .modal-button-cancel:hover {
            background: linear-gradient(135deg, #868e96 0%, #adb5bd 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(108, 117, 125, 0.4);
        }

        .modal-button-confirm {
            background: linear-gradient(135deg, var(--accent-blue) 0%, #5dade2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }

        .modal-button-confirm:hover {
            background: linear-gradient(135deg, #5dade2 0%, #85c1e9 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
        }

        .modal-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .modal-button.loading {
            pointer-events: none;
            opacity: 0.8;
        }

        .modal-button.loading::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        /* 按钮脉冲动画效果 */
        @keyframes buttonPulse {
            0% { box-shadow: 0 3px 12px rgba(0, 0, 0, 0.15); }
            50% { box-shadow: 0 5px 20px rgba(0, 0, 0, 0.25); }
            100% { box-shadow: 0 3px 12px rgba(0, 0, 0, 0.15); }
        }

        .game-button:focus {
            outline: none;
            animation: buttonPulse 1.5s infinite;
        }

        /* 按钮加载状态 */
        .game-button.loading {
            pointer-events: none;
            opacity: 0.7;
            position: relative;
        }

        .game-button.loading::after {
            content: '';
            position: absolute;
            width: 12px;
            height: 12px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: buttonSpin 1s linear infinite;
            right: 8px;
        }

        @keyframes buttonSpin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 按钮成功状态 */
        .game-button.success {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 50%, #58d68d 100%) !important;
            animation: successPulse 0.6s ease-out;
        }

        @keyframes successPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        /* 按钮图标动画 */
        .game-button span {
            transition: transform 0.3s ease;
            display: inline-block;
        }

        .game-button:hover span {
            transform: scale(1.2) rotate(5deg);
        }

        .button-download:hover span {
            animation: sparkle 0.6s ease-in-out;
        }

        @keyframes sparkle {
            0%, 100% { transform: scale(1.2) rotate(0deg); }
            25% { transform: scale(1.3) rotate(-5deg); }
            50% { transform: scale(1.4) rotate(5deg); }
            75% { transform: scale(1.3) rotate(-3deg); }
        }

        .button-install:hover span {
            animation: bounce 0.6s ease-in-out;
        }

        @keyframes bounce {
            0%, 100% { transform: scale(1.2) translateY(0); }
            50% { transform: scale(1.3) translateY(-3px); }
        }

        /* 按钮组整体动画 */
        .game-buttons {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
            align-items: center;
        }
        
        .pagination button {
            padding: 8px 15px;
            background-color: var(--card-bg);
            border: none;
            border-radius: 20px;
            cursor: pointer;
            transition: background-color 0.2s;
            font-weight: 500;
            height: 36px;
            line-height: 20px;
        }
        
        .pagination button:hover {
            background-color: var(--accent-blue);
            color: white;
        }
        
        .pagination button:disabled {
            background-color: rgba(0, 0, 0, 0.05);
            color: rgba(0, 0, 0, 0.3);
            cursor: not-allowed;
        }
        
        .pagination span {
            line-height: 36px;
            vertical-align: middle;
            font-size: 14px;
            color: var(--text-dark);
        }
        
        #loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
        }
        
        .spinner {
            width: 60px;
            height: 60px;
            border: 6px solid rgba(102, 126, 234, 0.1);
            border-left-color: #667eea;
            border-top-color: #764ba2;
            border-radius: 50%;
            animation: spin 1s linear infinite, pulse 2s ease-in-out infinite;
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        @keyframes pulse {
            0%, 100% {
                box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
            }
            50% {
                box-shadow: 0 0 30px rgba(102, 126, 234, 0.6);
            }
        }
        
        #error-message {
            padding: 20px;
            text-align: center;
            color: #e74c3c;
            background-color: rgba(231, 76, 60, 0.1);
            border-radius: 8px;
        }
        
        /* 游戏详情弹窗样式 - 现代化设计 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.6));
            backdrop-filter: blur(8px);
            z-index: 1000;
            overflow-y: auto;
            padding: 20px;
            box-sizing: border-box;
        }

        .modal-content {
            position: relative;
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            margin: 0 auto;
            padding: 0;
            width: 100%;
            max-width: 1000px;
            border-radius: 20px;
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.25),
                0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.6);
            animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
            overflow: hidden;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 25px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            position: relative;
        }

        .modal-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
            pointer-events: none;
        }

        .modal-header h2 {
            margin: 0;
            font-size: 24px;
            font-weight: 700;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            letter-spacing: 0.5px;
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        /* Denuvo加密游戏警告样式 */
        .denuvo-game-title {
            color: #ff4757 !important;
            text-shadow: 0 2px 4px rgba(255, 71, 87, 0.4);
            animation: denuvoGlow 2s ease-in-out infinite alternate;
        }

        .denuvo-warning {
            background: linear-gradient(135deg, #ff4757, #ff3742);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            box-shadow: 0 4px 15px rgba(255, 71, 87, 0.3);
            border: 2px solid rgba(255, 255, 255, 0.2);
            animation: denuvoWarningPulse 3s ease-in-out infinite;
            white-space: nowrap;
        }

        @keyframes denuvoGlow {
            0% {
                text-shadow: 0 2px 4px rgba(255, 71, 87, 0.4);
            }
            100% {
                text-shadow: 0 2px 4px rgba(255, 71, 87, 0.8), 0 0 20px rgba(255, 71, 87, 0.3);
            }
        }

        @keyframes denuvoWarningPulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 4px 15px rgba(255, 71, 87, 0.3);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 6px 20px rgba(255, 71, 87, 0.5);
            }
        }

        .close-button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            font-size: 20px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
        }

        .close-button:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: rotate(90deg) scale(1.1);
        }
        
        #modal-loading {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 300px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            padding: 30px;
        }

        .loading-text {
            margin-top: 20px;
            color: #667eea;
            font-size: 16px;
            font-weight: 600;
            animation: pulse 2s ease-in-out infinite;
        }

        /* 骨架屏样式 */
        .skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% {
                background-position: 200% 0;
            }
            100% {
                background-position: -200% 0;
            }
        }

        .skeleton-header {
            width: 100%;
            height: 200px;
            border-radius: 15px;
            margin-bottom: 20px;
        }

        .skeleton-text {
            height: 20px;
            border-radius: 10px;
            margin-bottom: 10px;
        }

        .skeleton-text.short {
            width: 60%;
        }

        .skeleton-text.medium {
            width: 80%;
        }

        .skeleton-text.long {
            width: 100%;
        }

        #modal-error {
            padding: 25px;
            text-align: center;
            color: #e74c3c;
            background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(231, 76, 60, 0.05));
            margin: 30px;
            border-radius: 15px;
            border: 1px solid rgba(231, 76, 60, 0.2);
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.1);
        }

        .modal-body {
            padding: 30px;
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
        }
        
        .game-basic-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
            padding: 25px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.9));
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .game-header {
            position: relative;
            overflow: hidden;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            height: 250px; /* 固定高度以适配横图 */
        }

        .game-header img {
            width: 100%;
            height: 100%;
            border-radius: 15px;
            transition: transform 0.3s ease;
            object-fit: cover; /* 保持比例裁剪 */
            object-position: center; /* 居中显示 */
        }

        .game-header:hover img {
            transform: scale(1.05);
        }

        .game-meta {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding: 10px 0;
        }

        .meta-row {
            margin-bottom: 15px;
            font-size: 16px;
            display: flex;
            align-items: center;
            padding: 12px 15px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 10px;
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .meta-row:hover {
            background: rgba(255, 255, 255, 0.8);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .meta-row::before {
            content: '';
            width: 8px;
            height: 8px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            margin-right: 12px;
            flex-shrink: 0;
        }

        .meta-label {
            font-weight: 700;
            color: #4a5568;
            margin-right: 15px;
            min-width: 80px;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            position: relative;
        }

        .meta-value {
            color: #2d3748;
            font-weight: 500;
            flex: 1;
        }

        .price-row {
            margin-top: 25px;
            padding: 15px 20px;
            background: linear-gradient(135deg, #48bb78, #38a169);
            border-radius: 12px;
            color: white;
            font-size: 24px;
            font-weight: 800;
            text-align: center;
            box-shadow: 0 8px 25px rgba(72, 187, 120, 0.3);
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        .game-description {
            margin-bottom: 40px;
            padding: 25px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.9));
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .game-description h3, .game-screenshots h3, .game-requirements h3 {
            font-size: 22px;
            margin-bottom: 20px;
            color: #2d3748;
            font-weight: 700;
            position: relative;
            padding-left: 15px;
        }

        .game-description h3::before, .game-screenshots h3::before, .game-requirements h3::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 24px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        #game-description-text {
            line-height: 1.8;
            color: #4a5568;
            max-height: 300px;
            overflow-y: auto;
            padding-right: 15px;
            font-size: 16px;
            text-align: justify;
        }

        #game-description-text::-webkit-scrollbar {
            width: 6px;
        }

        #game-description-text::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 3px;
        }

        #game-description-text::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 3px;
        }

        .game-screenshots {
            margin-bottom: 40px;
            padding: 25px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.9));
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        #screenshots-container {
            display: flex;
            overflow-x: auto;
            gap: 20px;
            padding: 15px 0 25px 0;
            scroll-snap-type: x mandatory;
        }

        #screenshots-container::-webkit-scrollbar {
            height: 8px;
        }

        #screenshots-container::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 4px;
        }

        #screenshots-container::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 4px;
        }

        .screenshot {
            flex: 0 0 auto;
            width: 320px;
            height: 180px;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            scroll-snap-align: start;
            position: relative;
            cursor: pointer;
        }

        .screenshot::before {
            content: '🔍 点击放大';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.6));
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            opacity: 0;
            transition: all 0.3s ease;
            z-index: 2;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .screenshot:hover::before {
            opacity: 1;
        }

        .screenshot::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .screenshot:hover::after {
            opacity: 1;
        }

        .screenshot img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.4s ease;
        }

        .screenshot:hover img {
            transform: scale(1.1);
        }
        
        .game-requirements {
            padding: 25px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.9));
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .requirements-tabs {
            display: flex;
            margin-bottom: 25px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 12px;
            padding: 5px;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .tab-button {
            flex: 1;
            padding: 12px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            position: relative;
            color: #718096;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .tab-button:hover {
            color: #4a5568;
            background: rgba(255, 255, 255, 0.5);
        }

        .tab-button.active {
            color: white;
            background: linear-gradient(135deg, #667eea, #764ba2);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            transform: translateY(-1px);
        }

        .requirements-content {
            display: none;
            line-height: 1.8;
            color: #4a5568;
            font-size: 15px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 12px;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .requirements-content.active {
            display: block;
            animation: fadeInUp 0.3s ease;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .modal {
                padding: 10px;
            }

            .modal-content {
                max-width: 100%;
                margin: 0;
                border-radius: 15px;
            }

            .modal-header {
                padding: 20px;
            }

            .modal-header h2 {
                font-size: 20px;
            }

            .modal-body {
                padding: 20px;
            }

            .game-basic-info {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px;
            }

            .game-header {
                order: -1;
                height: 200px; /* 移动端适配高度 */
            }

            .meta-row {
                font-size: 14px;
                margin-bottom: 12px;
            }

            .meta-label {
                min-width: 70px;
                font-size: 12px;
            }

            .price-row {
                font-size: 20px;
                padding: 12px 15px;
            }

            .game-description, .game-screenshots, .game-requirements {
                padding: 20px;
            }

            .game-description h3, .game-screenshots h3, .game-requirements h3 {
                font-size: 18px;
            }

            #game-description-text {
                font-size: 14px;
            }

            .screenshot {
                width: 280px;
                height: 157px;
            }

            .requirements-tabs {
                flex-direction: column;
                gap: 5px;
            }

            .tab-button {
                padding: 10px 15px;
                font-size: 14px;
            }

            .requirements-content {
                font-size: 14px;
                padding: 15px;
            }
        }

        @media (max-width: 480px) {
            .modal-header {
                padding: 15px;
            }

            .modal-header h2 {
                font-size: 18px;
                flex-direction: column;
                gap: 8px;
                align-items: flex-start;
            }

            .denuvo-warning {
                font-size: 12px;
                padding: 4px 8px;
                border-radius: 15px;
            }

            .modal-body {
                padding: 15px;
            }

            .game-basic-info, .game-description, .game-screenshots, .game-requirements {
                padding: 15px;
            }

            .game-header {
                height: 180px; /* 小屏幕设备头图高度 */
            }

            .screenshot {
                width: 240px;
                height: 135px;
            }

            .close-button {
                width: 35px;
                height: 35px;
                font-size: 18px;
            }
        }

        /* 图片全屏查看样式 */
        .fullscreen-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            z-index: 1100;
            overflow: hidden;
            text-align: center;
            justify-content: center;
            align-items: center;
        }
        
        .close-fullscreen-button {
            position: absolute;
            top: 20px;
            right: 30px;
            font-size: 40px;
            color: white;
            cursor: pointer;
            z-index: 1110;
            text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
            transition: color 0.2s;
        }
        
        .close-fullscreen-button:hover {
            color: #f8f8f8;
        }
        
        #fullscreen-image {
            max-width: 90%;
            max-height: 90%;
            object-fit: contain;
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.5);
        }
        
        /* 可复制元素样式 */
        .copyable {
            cursor: pointer;
            position: relative;
            display: inline-block;
            transition: all 0.2s;
        }
        
        .copyable:hover {
            color: var(--accent-blue);
            text-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
        }
        
        .copyable.copied {
            color: var(--accent-green);
            text-shadow: 0 0 5px rgba(76, 175, 80, 0.3);
            animation: pulseCopy 0.5s;
        }
        
        @keyframes pulseCopy {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        /* 美化通知系统样式 */
        #toast-container {
            position: fixed;
            top: 25px;
            right: 25px;
            z-index: 9999;
            pointer-events: none;
            max-width: 400px;
        }

        .toast {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 249, 250, 0.95));
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 16px;
            padding: 16px 20px;
            margin-bottom: 12px;
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.1),
                0 8px 25px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.6);
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 15px;
            font-weight: 500;
            max-width: 380px;
            min-width: 280px;
            opacity: 0;
            transform: translateX(100px) scale(0.8);
            transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
            word-break: break-word;
            position: relative;
            overflow: hidden;
        }

        .toast::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .toast:hover::before {
            opacity: 1;
        }

        .toast.show {
            opacity: 1;
            transform: translateX(0) scale(1);
        }

        .toast-icon {
            font-size: 22px;
            flex-shrink: 0;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }
        
        .toast-success {
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(139, 195, 74, 0.05));
            border: 1px solid rgba(76, 175, 80, 0.2);
            color: #2e7d32;
        }

        .toast-success .toast-icon {
            background: linear-gradient(135deg, #4caf50, #66bb6a);
            color: white;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }

        .toast-error {
            background: linear-gradient(135deg, rgba(244, 67, 54, 0.1), rgba(239, 83, 80, 0.05));
            border: 1px solid rgba(244, 67, 54, 0.2);
            color: #c62828;
        }

        .toast-error .toast-icon {
            background: linear-gradient(135deg, #f44336, #ef5350);
            color: white;
            box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
        }

        .toast-info {
            background: linear-gradient(135deg, rgba(33, 150, 243, 0.1), rgba(66, 165, 245, 0.05));
            border: 1px solid rgba(33, 150, 243, 0.2);
            color: #1565c0;
        }

        .toast-info .toast-icon {
            background: linear-gradient(135deg, #2196f3, #42a5f5);
            color: white;
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
        }

        .toast-warning {
            background: linear-gradient(135deg, rgba(255, 152, 0, 0.1), rgba(255, 183, 77, 0.05));
            border: 1px solid rgba(255, 152, 0, 0.2);
            color: #ef6c00;
        }

        .toast-warning .toast-icon {
            background: linear-gradient(135deg, #ff9800, #ffb74d);
            color: white;
            box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
        }

        .toast-content {
            flex: 1;
            line-height: 1.4;
            font-weight: 500;
        }

        /* 通知进度条 */
        .toast::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            height: 3px;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.4));
            border-radius: 0 0 16px 16px;
            animation: toastProgress 3s linear;
        }

        @keyframes toastProgress {
            from {
                width: 100%;
            }
            to {
                width: 0%;
            }
        }

        /* 通知堆叠效果 */
        .toast:nth-child(1) { z-index: 100; }
        .toast:nth-child(2) { z-index: 99; transform: translateY(-5px) scale(0.98); }
        .toast:nth-child(3) { z-index: 98; transform: translateY(-10px) scale(0.96); }
        .toast:nth-child(4) { z-index: 97; transform: translateY(-15px) scale(0.94); }

        .toast:nth-child(n+5) {
            display: none; /* 最多显示4个通知 */
        }

        /* 悬停效果 */
        .toast:hover {
            transform: translateX(-5px) scale(1.02);
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.15),
                0 10px 30px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
        }

        .toast:hover::after {
            animation-play-state: paused; /* 悬停时暂停进度条 */
        }
        
        /* 通知系统移动端适配 */
        @media (max-width: 768px) {
            #toast-container {
                top: 15px;
                right: 15px;
                left: 15px;
                max-width: none;
            }

            .toast {
                max-width: none;
                min-width: auto;
                margin-bottom: 10px;
                padding: 14px 16px;
                font-size: 14px;
                border-radius: 12px;
            }

            .toast-icon {
                font-size: 20px;
                width: 28px;
                height: 28px;
            }
        }

        @media (max-width: 480px) {
            #toast-container {
                top: 10px;
                right: 10px;
                left: 10px;
            }

            .toast {
                padding: 12px 14px;
                font-size: 13px;
                border-radius: 10px;
            }

            .toast-icon {
                font-size: 18px;
                width: 26px;
                height: 26px;
            }
        }

        /* 快捷功能按钮样式 */
        .quick-actions {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }

        .quick-actions-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 15px;
            opacity: 0.8;
        }

        .quick-buttons {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }

        .quick-button {
            position: relative;
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            overflow: hidden;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 160px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            flex: 1;
        }

        .quick-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s;
        }

        .quick-button:hover::before {
            left: 100%;
        }

        .quick-button:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }

        .quick-button:active {
            transform: translateY(0) scale(0.98);
        }

        /* 第一个按钮 - 游戏按钮美化 */
        .quick-button.button-games {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
            color: white;
            position: relative;
            overflow: hidden;
            box-shadow:
                0 8px 32px rgba(102, 126, 234, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            border: 2px solid transparent;
            background-clip: padding-box;
        }

        .quick-button.button-games::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: rotate 3s linear infinite;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .quick-button.button-games::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.6s ease-out;
            border-radius: 25px;
        }

        .quick-button.button-games:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow:
                0 15px 45px rgba(102, 126, 234, 0.6),
                0 0 0 1px rgba(255, 255, 255, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .quick-button.button-games:hover::before {
            opacity: 1;
        }

        .quick-button.button-games:hover::after {
            left: 100%;
        }

        .quick-button.button-games span {
            position: relative;
            z-index: 2;
            transition: all 0.3s ease;
        }

        .quick-button.button-games:hover span {
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
        }

        .quick-button.button-games .button-icon {
            display: inline-block;
            transition: transform 0.3s ease;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        }

        .quick-button.button-games:hover .button-icon {
            transform: scale(1.2) rotate(10deg);
            animation: gameIconBounce 0.6s ease-in-out;
        }

        @keyframes gameIconBounce {
            0%, 100% { transform: scale(1.2) rotate(10deg); }
            50% { transform: scale(1.4) rotate(-5deg) translateY(-2px); }
        }

        /* 第二个按钮 - 教程按钮美化 */
        .quick-button.button-tutorial {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 50%, #f093fb 100%);
            color: white;
            position: relative;
            overflow: hidden;
            box-shadow:
                0 8px 32px rgba(245, 87, 108, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            border: 2px solid transparent;
            background-clip: padding-box;
        }

        .quick-button.button-tutorial::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #f093fb, #f5576c, #ff6b9d, #f093fb);
            background-size: 300% 300%;
            border-radius: 27px;
            z-index: -1;
            animation: gradientShift 3s ease infinite;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .quick-button.button-tutorial::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.6s ease-out;
            border-radius: 25px;
        }

        .quick-button.button-tutorial:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow:
                0 15px 45px rgba(245, 87, 108, 0.6),
                0 0 0 1px rgba(255, 255, 255, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .quick-button.button-tutorial:hover::before {
            opacity: 1;
        }

        .quick-button.button-tutorial:hover::after {
            left: 100%;
        }

        .quick-button.button-tutorial span {
            position: relative;
            z-index: 2;
            transition: all 0.3s ease;
        }

        .quick-button.button-tutorial:hover span {
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
        }

        .quick-button.button-tutorial .button-icon {
            display: inline-block;
            transition: transform 0.3s ease;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        }

        .quick-button.button-tutorial:hover .button-icon {
            transform: scale(1.2) rotate(-10deg);
            animation: bookIconFlip 0.8s ease-in-out;
        }

        @keyframes bookIconFlip {
            0% { transform: scale(1.2) rotate(-10deg); }
            25% { transform: scale(1.3) rotateY(180deg) rotate(5deg); }
            50% { transform: scale(1.4) rotateY(360deg) rotate(-5deg); }
            75% { transform: scale(1.3) rotate(8deg); }
            100% { transform: scale(1.2) rotate(-10deg); }
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* 按钮图标增强 */
        .button-icon {
            margin-right: 12px;
            font-size: 20px;
            display: inline-block;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            animation: float 3s ease-in-out infinite;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-3px);
            }
        }

        /* 按钮点击效果增强 */
        .quick-button:active {
            transform: translateY(-1px) scale(0.98) !important;
            transition: transform 0.1s ease;
        }

        .quick-button.button-games:active {
            box-shadow:
                0 5px 20px rgba(102, 126, 234, 0.8),
                inset 0 3px 10px rgba(0, 0, 0, 0.2) !important;
        }

        .quick-button.button-tutorial:active {
            box-shadow:
                0 5px 20px rgba(245, 87, 108, 0.8),
                inset 0 3px 10px rgba(0, 0, 0, 0.2) !important;
        }

        /* 按钮加载状态 */
        .quick-button.loading {
            pointer-events: none;
            opacity: 0.8;
            position: relative;
        }

        .quick-button.loading .button-icon {
            animation: loadingSpin 1s linear infinite;
        }

        @keyframes loadingSpin {
            0% { transform: rotate(0deg) translateY(0px); }
            25% { transform: rotate(90deg) translateY(-1px); }
            50% { transform: rotate(180deg) translateY(-2px); }
            75% { transform: rotate(270deg) translateY(-1px); }
            100% { transform: rotate(360deg) translateY(0px); }
        }

        /* 按钮成功状态 */
        .quick-button.success {
            animation: successPulse 0.6s ease-out;
        }

        @keyframes successPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* 按钮焦点状态 */
        .quick-button:focus {
            outline: none;
        }

        .quick-button.button-games:focus {
            box-shadow:
                0 0 0 3px rgba(102, 126, 234, 0.3),
                0 8px 32px rgba(102, 126, 234, 0.4) !important;
        }

        .quick-button.button-tutorial:focus {
            box-shadow:
                0 0 0 3px rgba(245, 87, 108, 0.3),
                0 8px 32px rgba(245, 87, 108, 0.4) !important;
        }

        /* 按钮文字发光效果 */
        .quick-button:hover span:not(.button-icon) {
            animation: textGlow 2s ease-in-out infinite alternate;
        }

        @keyframes textGlow {
            from {
                text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
            }
            to {
                text-shadow:
                    0 0 10px rgba(255, 255, 255, 0.8),
                    0 0 20px rgba(255, 255, 255, 0.6),
                    0 0 30px rgba(255, 255, 255, 0.4);
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 70px;
                padding: 15px 0;
            }

            .sidebar-icon {
                width: 45px;
                height: 45px;
                border-radius: 15px;
            }

            .sidebar-icon-symbol {
                font-size: 18px;
            }

            .sidebar-icon-text {
                font-size: 9px;
            }

            .search-container {
                width: 100%;
            }

            .greeting-card {
                flex-direction: column;
                text-align: center;
            }

            .greeting-stats {
                margin-top: 15px;
            }

            .main-container {
                padding: 20px 15px;
            }

            .quick-buttons {
                flex-direction: column;
                gap: 10px;
            }

            .quick-button {
                min-width: auto;
                padding: 10px 16px;
                font-size: 13px;
            }
        }

        @media (max-width: 480px) {
            .sidebar {
                width: 60px;
                padding: 10px 0;
            }

            .sidebar-icon {
                width: 40px;
                height: 40px;
                border-radius: 12px;
                margin-bottom: 3px;
            }

            .sidebar-icon-symbol {
                font-size: 16px;
                margin-bottom: 1px;
            }

            .sidebar-icon-text {
                font-size: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <!-- 导航菜单 -->
        <nav class="sidebar-nav">
            <a href="/" class="sidebar-icon active" title="首页 - 浏览游戏列表">
                <div class="sidebar-icon-symbol">🏠</div>
                <div class="sidebar-icon-text">首页</div>
            </a>

            <a href="/管理" class="sidebar-icon" title="管理页面 - 游戏管理">
                <div class="sidebar-icon-symbol">📊</div>
                <div class="sidebar-icon-text">管理</div>
            </a>

            <a href="/附加功能" class="sidebar-icon" title="附加功能 - 扩展工具">
                <div class="sidebar-icon-symbol">🔧</div>
                <div class="sidebar-icon-text">附加功能</div>
            </a>

            <a href="/设置" class="sidebar-icon" title="设置 - 系统配置">
                <div class="sidebar-icon-symbol">⚙️</div>
                <div class="sidebar-icon-text">设置</div>
            </a>

            <a href="https://wish.steamlab.cc/" class="sidebar-icon" title="许愿 - 游戏许愿" target="_blank">
                <div class="sidebar-icon-symbol">🌟</div>
                <div class="sidebar-icon-text">许愿</div>
            </a>

            <a href="https://help.steamlab.cc/%E4%B8%BB%E9%A1%B5.html" class="sidebar-icon" title="会员申请 - 申请会员服务" target="_blank">
                <div class="sidebar-icon-symbol">👑</div>
                <div class="sidebar-icon-text">会员申请</div>
            </a>

            <a href="https://help.steamlab.cc/%E5%94%AE%E5%90%8E%E5%B7%A5%E5%8D%95.html" class="sidebar-icon" title="售后工单 - 提交售后服务" target="_blank">
                <div class="sidebar-icon-symbol">🎫</div>
                <div class="sidebar-icon-text">售后工单</div>
            </a>
        </nav>
    </div>
    
    <div class="main-container">
        <div class="header">
            <div class="greeting-card">
                <div class="greeting-text">
                    <h1>SteamVault Pro - Steam 宝库</h1>
                    <p>欢迎使用 Steam 宝库平台，快速查找和管理您需要的游戏资源</p>
                </div>
                <div class="greeting-stats">
                    <span id="totalGames">...</span>
                    <small>款游戏可用</small>
                </div>
            </div>
            
            <div class="search-wrapper">
                <div class="search-container">
                    <div class="search-title">快速搜索</div>
                    <div class="search-box">
                        <div class="search-icon">🔍</div>
                        <input type="text" placeholder="输入游戏名称或AppID..." id="searchInput" autofocus>
                    </div>
                    <div class="search-options">
                        <div class="search-option active">全部</div>
                        <div class="search-option">按名称</div>
                        <div class="search-option">按AppID</div>
                    </div>

                    <!-- 快捷功能按钮 -->
                    <div class="quick-actions">
                        <div class="quick-actions-title">快捷功能</div>
                        <div class="quick-buttons">
                            <button class="quick-button button-games" onclick="handleGamesButton()">
                                <span class="button-icon">🎮</span>
                                <span>小白专用——批量入库常见热门游戏</span>
                            </button>
                            <button class="quick-button button-tutorial" onclick="handleTutorialButton()">
                                <span class="button-icon">📚</span>
                                <span>使用教程</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="games-container">
            <div class="container-header">
                <div class="container-title">游戏列表</div>
            </div>
            
            <div id="loading">
                <div class="spinner"></div>
            </div>
            
            <div id="error-message" style="display: none;"></div>
            
            <div id="games-list" class="games-list"></div>
            
            <div class="pagination">
                <button id="prev-page" disabled>上一页</button>
                <span id="page-info">第 1 页 / 共 2 页</span>
                <button id="next-page">下一页</button>
            </div>
        </div>
    </div>

    <!-- 游戏详情弹窗 -->
    <div id="game-details-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modal-game-title">游戏详情</h2>
                <span class="close-button">&times;</span>
            </div>
            <div id="modal-loading">
                <div class="spinner"></div>
                <div class="loading-text">正在加载游戏详情...</div>
            </div>
            <div id="modal-error" style="display: none;"></div>
            <div id="modal-content" class="modal-body" style="display: none;">
                <div class="game-basic-info" data-aos="fade-up" data-aos-delay="100">
                    <div class="game-header" data-aos="zoom-in" data-aos-delay="200">
                        <img id="game-header-image" src="" alt="游戏图片">
                    </div>
                    <div class="game-meta" data-aos="fade-left" data-aos-delay="300">
                        <div class="meta-row">
                            <span class="meta-label">类型</span>
                            <span class="meta-value" id="game-type"></span>
                        </div>
                        <div class="meta-row">
                            <span class="meta-label">发布日期</span>
                            <span class="meta-value" id="game-release-date"></span>
                        </div>
                        <div class="meta-row">
                            <span class="meta-label">开发商</span>
                            <span class="meta-value" id="game-developers"></span>
                        </div>
                        <div class="meta-row">
                            <span class="meta-label">发行商</span>
                            <span class="meta-value" id="game-publishers"></span>
                        </div>
                        <div class="price-row">
                            <span id="game-price"></span>
                        </div>
                    </div>
                </div>
                <div class="game-description" data-aos="fade-up" data-aos-delay="400">
                    <h3>简介</h3>
                    <div id="game-description-text"></div>
                </div>
                <div class="game-screenshots">
                    <h3>游戏截图</h3>
                    <div id="screenshots-container"></div>
                </div>
                <div class="game-requirements" data-aos="fade-up" data-aos-delay="600">
                    <h3>系统需求</h3>
                    <div class="requirements-tabs">
                        <button class="tab-button active" data-tab="minimum">最低配置</button>
                        <button class="tab-button" data-tab="recommended">推荐配置</button>
                    </div>
                    <div id="minimum-requirements" class="requirements-content active"></div>
                    <div id="recommended-requirements" class="requirements-content"></div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 图片放大查看弹窗 -->
    <div id="image-viewer-modal" class="fullscreen-modal">
        <span class="close-fullscreen-button">&times;</span>
        <img id="fullscreen-image" src="" alt="全屏图片">
    </div>

    <!-- 报告错误模态框 -->
    <div id="report-error-modal" class="modal" style="display: none;">
        <div class="modal-content report-modal-content">
            <div class="modal-header">
                <h2 id="report-modal-title">报告游戏错误</h2>
                <span class="close-button" onclick="closeReportModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="report-game-info">
                    <h3 id="report-game-name"></h3>
                    <p id="report-game-details"></p>
                </div>
                <div class="error-types">
                    <h4>请选择错误类型：</h4>
                    <div class="error-type-options">
                        <label class="error-type-option">
                            <input type="radio" name="errorType" value="游戏需要更新">
                            <span class="error-type-text">🔄 游戏需要更新</span>
                        </label>
                        <label class="error-type-option">
                            <input type="radio" name="errorType" value="游戏无法下载">
                            <span class="error-type-text">❌ 游戏无法下载</span>
                        </label>
                        <label class="error-type-option">
                            <input type="radio" name="errorType" value="无DLC或DLC不全">
                            <span class="error-type-text">📦 无DLC或DLC不全</span>
                        </label>
                        <label class="error-type-option">
                            <input type="radio" name="errorType" value="需要该游戏的联机补丁">
                            <span class="error-type-text">🌐 需要该游戏的联机补丁</span>
                        </label>
                    </div>
                </div>
                <div class="modal-actions">
                    <button class="modal-button modal-button-cancel" onclick="closeReportModal()">取消</button>
                    <button class="modal-button modal-button-confirm" onclick="submitErrorReport()">确认提交</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量入库确认对话框 -->
    <div id="batch-install-modal" class="modal" style="display: none;">
        <div class="modal-content batch-install-modal-content">
            <div class="modal-header">
                <h2>🎮 批量入库热门游戏</h2>
                <span class="close-button" onclick="closeBatchInstallModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="batch-install-info">
                    <div class="feature-highlights">
                        <h3>✨ 功能亮点：</h3>
                        <ul class="feature-list">
                            <li>• 批量入库千款热门游戏，98%以上可即下即玩</li>
                            <li>• 免去繁琐搜索，新手友好型设计</li>
                        </ul>
                    </div>

                    <div class="usage-guide">
                        <h3>🛠️ 使用方法：</h3>
                        <ul class="usage-list">
                            <li>完成入库后直接启动Steam客户端</li>
                            <li>在游戏库中即可查看所有新增游戏</li>
                            <li style="color: #ff4444; font-weight: bold;">⚠️ 如需移除入库的游戏，在管理页面点击移除全部按钮即可移除</li>
                        </ul>
                    </div>

                    <div class="warm-tips">
                        <h3>💡 温馨提示：</h3>
                        <div class="tips-content">
                            <p>本功能特别适合：</p>
                            <ul class="tips-list">
                                <li>✅ 刚接触软件的新手玩家</li>
                                <li>✅ 追求快速体验的休闲用户</li>
                                
                            </ul>
                            <p class="advanced-tip">资深玩家建议使用精准入库功能，按需添加游戏体验更佳~</p>
                        </div>
                    </div>
                </div>

                <div class="modal-actions">
                    <button class="modal-button modal-button-cancel" onclick="closeBatchInstallModal()">取消</button>
                    <button class="modal-button modal-button-confirm" onclick="confirmBatchInstall()">确定执行</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 通知系统 -->
    <div id="toast-container"></div>

    <script>
        const PAGE_SIZE = 20; // 每页显示的游戏数量
        let allGames = [];
        let filteredGames = [];
        let currentPage = 1;
        let searchField = "all"; // 默认搜索全部字段
        
        // 获取游戏列表数据
        async function fetchGames() {
            try {
                const response = await fetch('/api/games');
                if (!response.ok) {
                    throw new Error('网络请求失败');
                }
                const data = await response.json();

                if (data.status === 'success') {
                    allGames = data.data;
                    filteredGames = [...allGames];

                    // 更新游戏总数
                    document.getElementById('totalGames').textContent = allGames.length;

                    document.getElementById('loading').style.display = 'none';
                    updatePageDisplay();
                    renderGames();
                } else {
                    throw new Error(data.message || '获取数据失败');
                }
            } catch (error) {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('error-message').style.display = 'block';
                document.getElementById('error-message').textContent = '获取游戏列表失败: ' + error.message;
            }
        }
        
        // 渲染游戏列表
        function renderGames() {
            const gamesListElement = document.getElementById('games-list');
            gamesListElement.innerHTML = '';
            
            const startIndex = (currentPage - 1) * PAGE_SIZE;
            const endIndex = Math.min(startIndex + PAGE_SIZE, filteredGames.length);
            const currentPageGames = filteredGames.slice(startIndex, endIndex);
            
            if (currentPageGames.length === 0) {
                if (filteredGames.length === 0 && allGames.length === 0) {
                    gamesListElement.innerHTML = `
                        <div style="text-align: center; padding: 40px; color: var(--text-light);">
                            <div style="font-size: 48px; margin-bottom: 20px;">🎮</div>
                            <h3 style="margin-bottom: 10px; color: var(--text-dark);">正在连接游戏服务器...</h3>
                            <p>如果长时间无法加载，请检查网络连接或稍后重试</p>
                            <button onclick="fetchGames()" style="margin-top: 20px; padding: 10px 20px; background: var(--accent-blue); color: white; border: none; border-radius: 8px; cursor: pointer;">重新加载</button>
                        </div>
                    `;
                } else {
                    gamesListElement.innerHTML = `
                        <div style="text-align: center; padding: 40px; color: var(--text-light);">
                            <div style="font-size: 48px; margin-bottom: 20px;">🔍</div>
                            <h3 style="margin-bottom: 10px; color: var(--text-dark);">没有找到匹配的游戏</h3>
                            <p>尝试调整搜索条件或清空搜索框</p>
                        </div>
                    `;
                }
                return;
            }
            
            currentPageGames.forEach(game => {
                const gameElement = document.createElement('div');
                gameElement.className = 'game-card';
                gameElement.dataset.appid = game.ID;
                
                gameElement.innerHTML = `
                    <div class="game-info">
                        <div class="game-name"><span class="copyable" data-copy="${game.名称}">${game.名称}</span></div>
                        <div class="game-details">
                            <span>AppID: <span class="copyable" data-copy="${game.ID}">${game.ID}</span></span>
                            <span class="game-tag game-type">${game.类型}</span>
                        </div>
                        <div class="game-buttons">
                            <button class="game-button button-download" data-appid="${game.ID}" data-name="${game.名称}" data-type="${game.类型}" title="一键下载并入库">
                                <span style="font-size: 14px;">⚡</span> 一键入库
                            </button>
                            <button class="game-button button-install" data-appid="${game.ID}" title="通过Steam安装游戏">
                                <span style="font-size: 14px;">🎮</span> 安装游戏
                            </button>
                            <button class="game-button button-store" data-appid="${game.ID}" title="在Steam商店查看">
                                <span style="font-size: 14px;">🛒</span> 商店页面
                            </button>
                            <button class="game-button button-db" data-appid="${game.ID}" title="在SteamDB查看">
                                <span style="font-size: 14px;">📊</span> SteamDB
                            </button>
                            <button class="game-button button-details" data-appid="${game.ID}" title="查看游戏详细信息">
                                <span style="font-size: 14px;">🔍</span> 查看详情
                            </button>
                            <button class="game-button button-report" data-appid="${game.ID}" data-name="${game.名称}" data-type="${game.类型}" title="报告游戏错误">
                                <span style="font-size: 14px;">⚠️</span> 报告错误
                            </button>
                        </div>
                    </div>
                    <div class="game-time">${game.时间}</div>
                `;
                
                // 为可复制元素添加点击事件
                gameElement.querySelectorAll('.copyable').forEach(el => {
                    el.addEventListener('click', (e) => {
                        e.stopPropagation(); // 阻止冒泡，防止触发游戏卡片的点击事件
                        copyToClipboard(el.dataset.copy, el);
                    });
                });
                
                // 为按钮添加点击事件
                gameElement.querySelectorAll('.button-install').forEach(button => {
                    button.addEventListener('click', (e) => {
                        e.stopPropagation(); // 阻止冒泡
                        const appId = button.dataset.appid;
                        window.location.href = `steam://install/${appId}`;
                    });
                });
                
                gameElement.querySelectorAll('.button-download').forEach(button => {
                    button.addEventListener('click', (e) => {
                        e.stopPropagation(); // 阻止冒泡
                        const appId = button.dataset.appid;
                        const gameName = button.dataset.name;
                        const gameType = button.dataset.type;
                        downloadGame(appId, gameName, gameType);
                    });
                });
                
                gameElement.querySelectorAll('.button-store').forEach(button => {
                    button.addEventListener('click', (e) => {
                        e.stopPropagation(); // 阻止冒泡
                        const appId = button.dataset.appid;
                        window.open(`https://store.steampowered.com/app/${appId}`, '_blank');
                    });
                });
                
                gameElement.querySelectorAll('.button-db').forEach(button => {
                    button.addEventListener('click', (e) => {
                        e.stopPropagation(); // 阻止冒泡
                        const appId = button.dataset.appid;
                        window.open(`https://steamdb.info/app/${appId}`, '_blank');
                    });
                });
                
                gameElement.querySelectorAll('.button-details').forEach(button => {
                    button.addEventListener('click', (e) => {
                        e.stopPropagation(); // 阻止冒泡
                        const appId = button.dataset.appid;
                        showGameDetails(appId);
                    });
                });

                gameElement.querySelectorAll('.button-report').forEach(button => {
                    button.addEventListener('click', (e) => {
                        e.stopPropagation(); // 阻止冒泡
                        const appId = button.dataset.appid;
                        const gameName = button.dataset.name;
                        const gameType = button.dataset.type;
                        showReportErrorModal(appId, gameName, gameType);
                    });
                });
                
                gamesListElement.appendChild(gameElement);
            });
            
            // 更新分页按钮状态
            document.getElementById('prev-page').disabled = currentPage === 1;
            document.getElementById('next-page').disabled = endIndex >= filteredGames.length;
            document.getElementById('page-info').textContent = `第 ${currentPage} 页 / 共 ${Math.ceil(filteredGames.length / PAGE_SIZE)} 页`;
        }
        
        // 复制文本到剪贴板
        function copyToClipboard(text, element) {
            navigator.clipboard.writeText(text).then(() => {
                // 添加复制成功效果到元素
                element.classList.add('copied');
                
                // 1秒后移除效果
                setTimeout(() => {
                    element.classList.remove('copied');
                }, 1000);
                
                // 显示飞入飞出通知 - 如果文本过长，只显示前20个字符
                const displayText = text.length > 20 ? text.substring(0, 20) + '...' : text;
                showNotification(`"${displayText}" 已复制到剪贴板！`, 'success');
            }).catch(err => {
                console.error('复制失败: ', err);
                showNotification('复制失败，请重试', 'error');
            });
        }
        
        // 美化通知系统
        function showNotification(message, type = 'success') {
            const toastContainer = document.getElementById('toast-container');
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;

            // 使用更美观的图标
            let iconContent = '✓';
            if (type === 'error') iconContent = '✕';
            if (type === 'info') iconContent = 'ℹ';
            if (type === 'warning') iconContent = '⚠';

            toast.innerHTML = `
                <div class="toast-icon">${iconContent}</div>
                <div class="toast-content">${message}</div>
            `;

            toastContainer.appendChild(toast);

            // 添加进入动画
            setTimeout(() => {
                toast.classList.add('show');
            }, 50);

            // 3秒后移除通知（延长显示时间）
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.remove();
                    }
                }, 400); // 等待退出动画完成
            }, 3000);

            // 添加点击关闭功能
            toast.style.pointerEvents = 'auto';
            toast.style.cursor = 'pointer';
            toast.addEventListener('click', () => {
                toast.classList.remove('show');
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.remove();
                    }
                }, 400);
            });
        }
        
        // 打开图片查看器
        function openImageViewer(imgUrl) {
            const modal = document.getElementById('image-viewer-modal');
            const fullscreenImg = document.getElementById('fullscreen-image');
            
            fullscreenImg.src = imgUrl;
            modal.style.display = 'flex';
            
            // 阻止滚动
            document.body.style.overflow = 'hidden';
        }
        
        // 关闭图片查看器
        function closeImageViewer() {
            const modal = document.getElementById('image-viewer-modal');
            modal.style.display = 'none';
            
            // 恢复滚动
            document.body.style.overflow = '';
        }
        
        // 重置模态框状态
        function resetModalState() {
            // 重置标题（清除HTML内容和样式）
            const titleElement = document.getElementById('modal-game-title');
            titleElement.textContent = '游戏详情';
            titleElement.innerHTML = '游戏详情';

            // 重置头图
            const headerImg = document.getElementById('game-header-image');
            headerImg.src = '';
            headerImg.style.opacity = '0';

            // 重置基本信息
            document.getElementById('game-type').textContent = '';
            document.getElementById('game-release-date').textContent = '';
            document.getElementById('game-developers').textContent = '';
            document.getElementById('game-publishers').textContent = '';
            document.getElementById('game-price').textContent = '';

            // 重置描述
            document.getElementById('game-description-text').innerHTML = '';

            // 重置截图容器
            const screenshotsContainer = document.getElementById('screenshots-container');
            screenshotsContainer.innerHTML = '';
            screenshotsContainer.scrollLeft = 0; // 重置滚动位置

            // 重置系统需求
            document.getElementById('minimum-requirements').innerHTML = '';
            document.getElementById('recommended-requirements').innerHTML = '';

            // 重置选项卡状态
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelectorAll('.requirements-content').forEach(content => {
                content.classList.remove('active');
            });
            // 激活第一个选项卡
            const firstTab = document.querySelector('.tab-button[data-tab="minimum"]');
            const firstContent = document.getElementById('minimum-requirements');
            if (firstTab && firstContent) {
                firstTab.classList.add('active');
                firstContent.classList.add('active');
            }

            // 清除动画类
            const modal = document.getElementById('game-details-modal');
            const modalContent = document.getElementById('modal-content');
            modal.classList.remove('animate__animated', 'animate__fadeIn', 'animate__fadeOut');
            modalContent.classList.remove('animate__animated', 'animate__fadeInUp', 'animate__fadeOutDown');
        }

        // 获取并显示游戏详情
        async function showGameDetails(appId) {
            const modal = document.getElementById('game-details-modal');
            const modalContent = document.getElementById('modal-content');
            const modalLoading = document.getElementById('modal-loading');
            const modalError = document.getElementById('modal-error');

            // 重置所有状态
            resetModalState();

            // 显示模态框和加载动画，添加动画效果
            modal.style.display = 'block';
            modal.classList.add('animate__animated', 'animate__fadeIn');
            modalLoading.style.display = 'flex';
            modalContent.style.display = 'none';
            modalError.style.display = 'none';

            try {
                const response = await fetch(`/api/game/${appId}`);
                if (!response.ok) {
                    throw new Error('网络请求失败');
                }

                const result = await response.json();
                if (result.status !== 'success') {
                    throw new Error(result.message || '获取游戏详情失败');
                }

                const gameData = result.data;
                if (!gameData[appId] || !gameData[appId].success) {
                    throw new Error('Steam API 未返回有效数据');
                }

                const data = gameData[appId].data;
                populateGameDetails(data);

                modalLoading.style.display = 'none';
                modalContent.style.display = 'block';
                modalContent.classList.add('animate__animated', 'animate__fadeInUp');
            } catch (error) {
                modalLoading.style.display = 'none';
                modalError.style.display = 'block';
                modalError.textContent = '获取游戏详情失败: ' + error.message;
            }
        }

        // 检测游戏是否包含Denuvo加密
        function checkForDenuvo(gameData) {
            // 将所有可能包含Denuvo信息的字段转换为小写字符串进行检测
            const fieldsToCheck = [
                gameData.short_description,
                gameData.detailed_description,
                gameData.about_the_game,
                gameData.pc_requirements?.minimum,
                gameData.pc_requirements?.recommended,
                JSON.stringify(gameData.drm_notice || ''),
                JSON.stringify(gameData.legal_notice || ''),
                JSON.stringify(gameData.content_descriptors || {}),
                JSON.stringify(gameData.categories || []),
                JSON.stringify(gameData.genres || [])
            ];

            // 检查每个字段是否包含"denuvo"关键词（不区分大小写）
            for (const field of fieldsToCheck) {
                if (field && typeof field === 'string' && field.toLowerCase().includes('denuvo')) {
                    console.log('检测到Denuvo加密游戏:', gameData.name);
                    console.log('检测到的字段内容:', field.substring(0, 200) + '...');
                    return true;
                }
            }

            return false;
        }

        // 填充游戏详情到模态框
        function populateGameDetails(gameData) {
            // 检测是否包含Denuvo加密
            const hasDenuvo = checkForDenuvo(gameData);

            // 设置标题
            const titleElement = document.getElementById('modal-game-title');
            const gameName = gameData.name || '游戏详情';

            if (hasDenuvo) {
                // 如果检测到Denuvo，标红游戏名称并添加警告
                titleElement.innerHTML = `
                    <span class="denuvo-game-title">${gameName}</span>
                    <span class="denuvo-warning">⚠️ D加密游戏</span>
                `;
            } else {
                titleElement.textContent = gameName;
            }
            
            // 设置头图，添加加载和错误处理
            const headerImg = document.getElementById('game-header-image');
            if (gameData.header_image) {
                headerImg.style.opacity = '0';
                headerImg.onload = function() {
                    this.style.transition = 'opacity 0.3s ease';
                    this.style.opacity = '1';
                };
                headerImg.onerror = function() {
                    this.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuaXoOazleWKoOi9veWbvueJhzwvdGV4dD48L3N2Zz4=';
                    this.style.opacity = '1';
                };
                headerImg.src = gameData.header_image;
            } else {
                headerImg.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuaXoOazleWKoOi9veWbvueJhzwvdGV4dD48L3N2Zz4=';
            }
            
            // 设置基本信息
            document.getElementById('game-type').textContent = gameData.type || '';
            document.getElementById('game-release-date').textContent = gameData.release_date ? gameData.release_date.date || '' : '';
            document.getElementById('game-developers').textContent = gameData.developers ? gameData.developers.join(', ') : '';
            document.getElementById('game-publishers').textContent = gameData.publishers ? gameData.publishers.join(', ') : '';
            
            // 设置价格信息
            if (gameData.is_free) {
                document.getElementById('game-price').textContent = '免费';
                document.getElementById('game-price').style.color = 'var(--accent-green)';
            } else if (gameData.price_overview) {
                document.getElementById('game-price').textContent = gameData.price_overview.final_formatted || '';
                
                // 如果有折扣，显示原价和折扣价
                if (gameData.price_overview.discount_percent > 0) {
                    const originalPrice = gameData.price_overview.initial_formatted || '';
                    const discountPercent = gameData.price_overview.discount_percent;
                    document.getElementById('game-price').innerHTML = `
                        <span style="color: var(--accent-green)">${gameData.price_overview.final_formatted}</span>
                        <span style="text-decoration: line-through; font-size: 16px; color: var(--text-light); margin-left: 10px;">
                            ${originalPrice}
                        </span>
                        <span style="background-color: var(--accent-green); color: white; padding: 2px 6px; border-radius: 4px; font-size: 14px; margin-left: 10px;">
                            -${discountPercent}%
                        </span>
                    `;
                }
            } else {
                document.getElementById('game-price').textContent = '价格信息不可用';
            }
            
            // 设置游戏简介
            if (gameData.short_description) {
                document.getElementById('game-description-text').innerHTML = gameData.short_description;
            } else {
                document.getElementById('game-description-text').textContent = '暂无游戏简介';
            }
            
            // 设置游戏截图
            const screenshotsContainer = document.getElementById('screenshots-container');
            screenshotsContainer.innerHTML = '';
            
            if (gameData.screenshots && gameData.screenshots.length > 0) {
                gameData.screenshots.forEach((screenshot, index) => {
                    const screenshotElement = document.createElement('div');
                    screenshotElement.className = 'screenshot';

                    // 为截图容器添加点击事件
                    screenshotElement.addEventListener('click', (e) => {
                        e.stopPropagation();
                        openImageViewer(screenshot.path_full);
                    });

                    const img = document.createElement('img');
                    img.alt = '游戏截图';
                    img.loading = 'lazy';

                    // 添加错误处理
                    img.onerror = function() {
                        this.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxMiIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuaXoOazleWKoOi9veaIquWbvzwvdGV4dD48L3N2Zz4=';
                    };

                    img.src = screenshot.path_thumbnail;

                    // 添加点击事件监听器，用于放大图片
                    img.addEventListener('click', (e) => {
                        e.stopPropagation(); // 阻止事件冒泡
                        openImageViewer(screenshot.path_full);
                    });

                    screenshotElement.appendChild(img);
                    screenshotsContainer.appendChild(screenshotElement);
                });
            } else {
                screenshotsContainer.innerHTML = '<p style="text-align: center; color: #999; padding: 40px;">暂无游戏截图</p>';
            }
            
            // 设置系统需求
            const minRequirements = document.getElementById('minimum-requirements');
            const recRequirements = document.getElementById('recommended-requirements');
            
            if (gameData.pc_requirements) {
                minRequirements.innerHTML = gameData.pc_requirements.minimum || '暂无最低配置信息';
                recRequirements.innerHTML = gameData.pc_requirements.recommended || '暂无推荐配置信息';
            } else {
                minRequirements.textContent = '暂无系统需求信息';
                recRequirements.textContent = '暂无系统需求信息';
            }
            
            // 激活系统需求选项卡点击事件
            setupTabButtons();

            // 重新初始化AOS动画
            setTimeout(() => {
                AOS.refresh();
            }, 100);
        }
        
        // 设置系统需求选项卡
        function setupTabButtons() {
            document.querySelectorAll('.tab-button').forEach(button => {
                button.addEventListener('click', function() {
                    // 移除所有active类
                    document.querySelectorAll('.tab-button').forEach(btn => {
                        btn.classList.remove('active');
                    });
                    document.querySelectorAll('.requirements-content').forEach(content => {
                        content.classList.remove('active');
                    });
                    
                    // 添加active类到当前点击的选项
                    this.classList.add('active');
                    const tab = this.dataset.tab;
                    document.getElementById(`${tab}-requirements`).classList.add('active');
                });
            });
        }
        
        // 关闭模态框
        function closeModal() {
            const modal = document.getElementById('game-details-modal');
            const modalContent = document.getElementById('modal-content');

            // 添加关闭动画
            modal.classList.remove('animate__fadeIn');
            modal.classList.add('animate__fadeOut');
            modalContent.classList.remove('animate__fadeInUp');
            modalContent.classList.add('animate__fadeOutDown');

            // 动画完成后隐藏模态框
            setTimeout(() => {
                modal.style.display = 'none';
                modal.classList.remove('animate__animated', 'animate__fadeOut');
                modalContent.classList.remove('animate__animated', 'animate__fadeOutDown');
            }, 400);
        }

        // 报告错误相关变量
        let currentReportData = {
            appId: '',
            gameName: '',
            gameType: ''
        };

        // 动态通知系统
        class DynamicNotification {
            constructor() {
                this.notifications = [];
                this.maxNotifications = 3;
            }

            show(message, type = 'info', duration = 4000) {
                // 如果通知数量超过最大值，移除最旧的通知
                if (this.notifications.length >= this.maxNotifications) {
                    this.remove(this.notifications[0]);
                }

                const notification = this.create(message, type);
                document.body.appendChild(notification);
                this.notifications.push(notification);

                // 显示动画
                requestAnimationFrame(() => {
                    notification.classList.add('show');
                });

                // 自动移除
                setTimeout(() => {
                    this.remove(notification);
                }, duration);

                return notification;
            }

            create(message, type) {
                const notification = document.createElement('div');
                notification.className = `dynamic-notification ${type}`;

                const icons = {
                    success: '✅',
                    error: '❌',
                    warning: '⚠️',
                    info: 'ℹ️'
                };

                notification.innerHTML = `
                    <div class="notification-icon">${icons[type] || icons.info}</div>
                    <div class="notification-content">${message}</div>
                    <div class="notification-close">×</div>
                `;

                // 点击关闭按钮
                const closeBtn = notification.querySelector('.notification-close');
                closeBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.remove(notification);
                });

                // 点击通知本身也可以关闭
                notification.addEventListener('click', () => {
                    this.remove(notification);
                });

                return notification;
            }

            remove(notification) {
                if (!notification || !notification.parentNode) return;

                // 移除动画
                notification.classList.remove('show');

                // 动画完成后移除元素
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }

                    // 从数组中移除
                    const index = this.notifications.indexOf(notification);
                    if (index > -1) {
                        this.notifications.splice(index, 1);
                    }
                }, 400);
            }

            success(message, duration) {
                return this.show(message, 'success', duration);
            }

            error(message, duration) {
                return this.show(message, 'error', duration);
            }

            warning(message, duration) {
                return this.show(message, 'warning', duration);
            }

            info(message, duration) {
                return this.show(message, 'info', duration);
            }
        }

        // 创建全局通知实例
        const dynamicNotification = new DynamicNotification();

        // 显示报告错误模态框
        window.showReportErrorModal = function(appId, gameName, gameType) {
            currentReportData = { appId, gameName, gameType };

            const modal = document.getElementById('report-error-modal');
            const gameNameElement = document.getElementById('report-game-name');
            const gameDetailsElement = document.getElementById('report-game-details');

            // 设置游戏信息
            gameNameElement.textContent = gameName;
            gameDetailsElement.textContent = `AppID: ${appId} | 类型: ${gameType}`;

            // 重置选择状态
            const radioButtons = modal.querySelectorAll('input[name="errorType"]');
            radioButtons.forEach(radio => radio.checked = false);

            // 显示模态框并添加动画
            modal.style.display = 'block';
            modal.classList.add('animate__animated', 'animate__fadeIn');

            // 为模态框内容添加动画
            const modalContent = modal.querySelector('.modal-content');
            modalContent.classList.add('animate__animated', 'animate__fadeInUp');
        }

        // 关闭报告错误模态框
        window.closeReportModal = function() {
            const modal = document.getElementById('report-error-modal');
            const modalContent = modal.querySelector('.modal-content');

            // 添加关闭动画
            modal.classList.remove('animate__fadeIn');
            modal.classList.add('animate__fadeOut');
            modalContent.classList.remove('animate__fadeInUp');
            modalContent.classList.add('animate__fadeOutDown');

            // 动画完成后隐藏模态框
            setTimeout(() => {
                modal.style.display = 'none';
                modal.classList.remove('animate__animated', 'animate__fadeOut');
                modalContent.classList.remove('animate__animated', 'animate__fadeOutDown');
            }, 400);
        }

        // 提交错误报告
        window.submitErrorReport = async function() {
            const modal = document.getElementById('report-error-modal');
            const selectedError = modal.querySelector('input[name="errorType"]:checked');

            if (!selectedError) {
                dynamicNotification.warning('请选择错误类型');
                return;
            }

            const confirmButton = modal.querySelector('.modal-button-confirm');
            const cancelButton = modal.querySelector('.modal-button-cancel');

            // 设置加载状态
            confirmButton.classList.add('loading');
            confirmButton.disabled = true;
            cancelButton.disabled = true;

            try {
                // 格式化当前时间
                const now = new Date();
                const currentTime = now.getFullYear() + '-' +
                    String(now.getMonth() + 1).padStart(2, '0') + '-' +
                    String(now.getDate()).padStart(2, '0') + ' ' +
                    String(now.getHours()).padStart(2, '0') + ':' +
                    String(now.getMinutes()).padStart(2, '0') + ':' +
                    String(now.getSeconds()).padStart(2, '0');

                // 构建优化的提交数据格式
                const reportData = JSON.stringify({
                    时间: currentTime,
                    游戏名称: currentReportData.gameName,
                    游戏类型: currentReportData.gameType,
                    AppID: currentReportData.appId,
                    错误类型: selectedError.value,
                    报告来源: "SteamVault Pro - Steam 宝库"
                }, null, 2);

                // 提交数据 - 通过后端代理
                const response = await fetch('/api/report-error', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        data: reportData
                    })
                });

                if (response.ok) {
                    const result = await response.json();

                    if (result.status === 'success') {
                        dynamicNotification.success('错误报告提交成功，感谢您的反馈！');
                        closeReportModal();
                    } else {
                        throw new Error(result.message || '提交失败');
                    }
                } else {
                    const errorText = await response.text();
                    throw new Error(`服务器响应错误: ${response.status} - ${errorText}`);
                }
            } catch (error) {
                let errorMessage = '提交失败，请检查网络连接后重试';
                if (error.message.includes('Failed to fetch')) {
                    errorMessage = '网络连接失败，请检查网络设置';
                } else if (error.message.includes('CORS')) {
                    errorMessage = '跨域请求被阻止，请联系管理员';
                } else if (error.message) {
                    errorMessage = `提交失败: ${error.message}`;
                }

                dynamicNotification.error(errorMessage);
            } finally {
                // 恢复按钮状态
                confirmButton.classList.remove('loading');
                confirmButton.disabled = false;
                cancelButton.disabled = false;
            }
        }
        
        // 更新页面显示
        function updatePageDisplay() {
            document.getElementById('page-info').textContent = `第 ${currentPage} 页 / 共 ${Math.ceil(filteredGames.length / PAGE_SIZE)} 页`;
        }
        
        // 搜索功能
        function searchGames(query) {
            if (!query.trim()) {
                filteredGames = [...allGames];
            } else {
                query = query.toLowerCase();
                
                // 根据选择的搜索字段进行过滤
                if (searchField === "name") {
                    filteredGames = allGames.filter(game => game.名称.toLowerCase().includes(query));
                } else if (searchField === "id") {
                    filteredGames = allGames.filter(game => game.ID.toString().includes(query));
                } else {
                    // 默认搜索全部字段
                    filteredGames = allGames.filter(game => 
                        game.名称.toLowerCase().includes(query) || 
                        game.ID.toString().includes(query)
                    );
                }
            }
            currentPage = 1;
            updatePageDisplay();
            renderGames();
        }
        
        // 添加事件监听器
        document.getElementById('prev-page').addEventListener('click', () => {
            if (currentPage > 1) {
                currentPage--;
                renderGames();
            }
        });
        
        document.getElementById('next-page').addEventListener('click', () => {
            if ((currentPage * PAGE_SIZE) < filteredGames.length) {
                currentPage++;
                renderGames();
            }
        });
        
        document.getElementById('searchInput').addEventListener('input', (e) => {
            searchGames(e.target.value);
        });
        
        // 搜索选项切换
        document.querySelectorAll('.search-option').forEach(option => {
            option.addEventListener('click', function() {
                // 移除所有active类
                document.querySelectorAll('.search-option').forEach(opt => {
                    opt.classList.remove('active');
                });
                
                // 添加active类到当前点击的选项
                this.classList.add('active');
                
                // 设置搜索字段
                const optionText = this.textContent;
                if (optionText === '按名称') {
                    searchField = "name";
                } else if (optionText === '按AppID') {
                    searchField = "id";
                } else {
                    searchField = "all";
                }
                
                // 重新执行搜索
                searchGames(document.getElementById('searchInput').value);
            });
        });
        
        // 关闭按钮事件处理
        document.querySelector('.close-button').addEventListener('click', closeModal);
        
        // 点击模态框外部区域关闭模态框
        window.addEventListener('click', (event) => {
            const gameModal = document.getElementById('game-details-modal');
            const reportModal = document.getElementById('report-error-modal');
            const batchInstallModal = document.getElementById('batch-install-modal');

            if (event.target === gameModal) {
                closeModal();
            }

            if (event.target === reportModal) {
                closeReportModal();
            }

            if (event.target === batchInstallModal) {
                closeBatchInstallModal();
            }
        });
        
        // 图片查看器关闭按钮事件
        document.querySelector('.close-fullscreen-button').addEventListener('click', closeImageViewer);
        
        // 点击图片查看器背景区域关闭
        document.getElementById('image-viewer-modal').addEventListener('click', (event) => {
            if (event.target === document.getElementById('image-viewer-modal')) {
                closeImageViewer();
            }
        });
        
        // 快捷功能按钮处理函数
        function handleGamesButton() {
            // 添加点击特效
            const button = event.target.closest('.quick-button');
            button.style.transform = 'translateY(0) scale(0.95)';
            setTimeout(() => {
                button.style.transform = '';
            }, 150);

            // 记录用户点击批量入库按钮的日志
            logUserAction('click_batch_install_button', {
                button_type: 'quick_button',
                action: 'show_batch_install_modal',
                page: 'index'
            });

            // 显示批量入库确认对话框
            showBatchInstallModal();
        }

        function handleTutorialButton() {
            // 添加点击特效
            const button = event.target.closest('.quick-button');
            button.style.transform = 'translateY(0) scale(0.95)';
            setTimeout(() => {
                button.style.transform = '';
            }, 150);

            // 记录用户点击教程按钮的日志
            logUserAction('click_tutorial_button', {
                button_type: 'quick_button',
                action: 'open_tutorial',
                tutorial_url: 'https://www.yuque.com/xiangjiaonigebunana-9d4l7/kb/uh8u0migt8ydpnfr?singleDoc#',
                page: 'index'
            });

            // 打开使用教程网站
            const tutorialUrl = 'https://www.yuque.com/xiangjiaonigebunana-9d4l7/kb/uh8u0migt8ydpnfr?singleDoc#';
            window.open(tutorialUrl, '_blank');

            // 显示提示消息
            showNotification('📚 正在打开《SteamVault Pro - Steam 宝库》使用教程...', 'info');
        }

        // 用户操作日志记录函数
        async function logUserAction(action, extraData = {}) {
            try {
                await fetch('/api/log-user-action', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: action,
                        extra: extraData,
                        timestamp: new Date().toISOString(),
                        user_agent: navigator.userAgent,
                        page_url: window.location.href
                    })
                });
            } catch (error) {
                // 静默失败，不影响用户体验
                console.debug('日志记录失败:', error);
            }
        }

        // 显示批量入库确认对话框
        function showBatchInstallModal() {
            const modal = document.getElementById('batch-install-modal');

            // 显示模态框
            modal.style.display = 'block';
            modal.classList.add('animate__animated', 'animate__fadeIn');

            // 为模态框内容添加动画
            const modalContent = modal.querySelector('.modal-content');
            modalContent.classList.add('animate__animated', 'animate__fadeInUp');
        }

        // 关闭批量入库确认对话框
        function closeBatchInstallModal() {
            const modal = document.getElementById('batch-install-modal');
            const modalContent = modal.querySelector('.modal-content');

            // 添加关闭动画
            modal.classList.remove('animate__fadeIn');
            modal.classList.add('animate__fadeOut');
            modalContent.classList.remove('animate__fadeInUp');
            modalContent.classList.add('animate__fadeOutDown');

            // 动画完成后隐藏模态框
            setTimeout(() => {
                modal.style.display = 'none';
                modal.classList.remove('animate__animated', 'animate__fadeOut');
                modalContent.classList.remove('animate__animated', 'animate__fadeOutDown');
            }, 400);
        }

        // 确认批量入库
        async function confirmBatchInstall() {
            // 记录用户确认批量入库操作
            logUserAction('confirm_batch_install', {
                action: 'start_batch_install',
                operation_type: 'batch_install_games'
            });

            // 关闭对话框
            closeBatchInstallModal();

            // 显示开始消息
            showNotification('🎮 开始批量入库热门游戏...', 'info');

            try {
                // 发送批量入库请求
                const response = await fetch('/api/batch-install', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const result = await response.json();

                if (result.status === 'success') {
                    // 记录批量入库成功日志
                    logUserAction('batch_install_success', {
                        success_count: result.count || 0,
                        failed_count: result.failed ? result.failed.length : 0,
                        operation_result: 'success'
                    });
                    showNotification(`✅ 批量入库完成！`, 'success');
                } else {
                    // 记录批量入库失败日志
                    logUserAction('batch_install_failed', {
                        error_message: result.message,
                        operation_result: 'failed'
                    });
                    showNotification(`❌ 批量入库失败}`, 'error');
                }
            } catch (error) {
                // 记录批量入库异常日志
                logUserAction('batch_install_error', {
                    error_message: error.message,
                    operation_result: 'error'
                });
                console.error('批量入库请求失败:', error);
                showNotification('❌ 批量入库请求失败，请检查网络连接', 'error');
            }
        }

        // 加载数据
        fetchGames();
        
        // 一键入库功能
        async function downloadGame(appId, gameName, gameType) {
            try {
                showNotification('正在获取游戏资源...', 'info');
                
                // 获取下载链接
                const response = await fetch('/api/game/download', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        appId: appId,
                        name: gameName,
                        type: gameType
                    })
                });
                
                const result = await response.json();
                
                if (result.status !== 'success') {
                    throw new Error(result.message || '获取下载链接失败');
                }
                
                showNotification(`开始处理游戏: ${gameName}`, 'info');
                
                // 发送请求给后端进行下载和解压
                const extractResponse = await fetch('/api/game/process', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        url: result.url,
                        name: gameName,
                        type: gameType,
                        appId: appId
                    })
                });
                
                // 处理已完成
                
                const extractResult = await extractResponse.json();

                if (extractResult.status !== 'success') {
                    throw new Error(extractResult.message || '处理文件失败');
                }

                // 构建详细的成功信息
                let successMessage = `游戏 ${gameName} 已成功入库`;

                // 检查DLC解锁信息
                if (extractResult.vdf_处理结果 && extractResult.vdf_处理结果.dlc_数量 > 0) {
                    const dlcCount = extractResult.vdf_处理结果.dlc_数量;
                    successMessage += ` - 解锁了 ${dlcCount} 个DLC`;
                }

                // 如果有移动结果信息，添加到消息中
                if (extractResult.移动结果 && extractResult.移动结果.status === 'success') {
                    const 移动详情 = extractResult.移动结果.结果;
                    if (移动详情.lua_moved && 移动详情.manifest_moved && 移动详情.cleanup_done) {
                        successMessage;
                    }
                }

                showNotification(successMessage, 'success');
            } catch (error) {
                console.error('下载游戏失败:', error);

                // 发送详细错误信息到日志服务
                fetch('/api/log/error', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        level: 'error',
                        message: '游戏入库处理失败',
                        extra: {
                            function: '一键入库',
                            game_name: gameName,
                            app_id: appId,
                            game_type: gameType,
                            error_message: error.message || error.toString(),
                            error_stack: error.stack || '',
                            user_action: '一键入库按钮点击',
                            failure_stage: '未知阶段'
                        }
                    })
                }).catch(() => {}); // 静默处理日志发送失败

                showNotification(`处理失败，请前往设置页面运行环境检测`, 'error');
            }
        }
        


        // 导航栏交互功能
        function initSidebar() {
            // 获取当前页面路径
            const currentPath = window.location.pathname;

            // 移除所有active类
            document.querySelectorAll('.sidebar-icon').forEach(icon => {
                icon.classList.remove('active');
            });

            // 根据当前路径设置active状态
            const pathMap = {
                '/': 0,
                '/管理': 1,
                '/附加功能': 2,
                '/设置': 3
            };

            const activeIndex = pathMap[currentPath];
            if (activeIndex !== undefined) {
                const sidebarIcons = document.querySelectorAll('.sidebar-icon');
                if (sidebarIcons[activeIndex]) {
                    sidebarIcons[activeIndex].classList.add('active');
                }
            }

            // 添加点击事件监听器
            document.querySelectorAll('.sidebar-icon').forEach(icon => {
                icon.addEventListener('click', function(e) {
                    // 如果是外部链接（许愿按钮），不处理active状态
                    if (this.getAttribute('target') === '_blank') {
                        return;
                    }

                    // 移除所有active类
                    document.querySelectorAll('.sidebar-icon').forEach(i => {
                        i.classList.remove('active');
                    });

                    // 添加active类到当前点击的图标
                    this.classList.add('active');
                });
            });
        }

        // 页面加载完成后初始化导航栏
        document.addEventListener('DOMContentLoaded', function() {
            initSidebar();
        });
    </script>

    <!-- AOS动画库JavaScript -->
    <script src="https://cdn.bootcdn.net/ajax/libs/aos/2.3.4/aos.js"></script>
    <script>
        // 初始化AOS动画
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true,
            offset: 100
        });
    </script>

    <!-- 版本测试警告弹窗 -->
    <div id="versionWarningModal" class="modal-overlay">
        <div class="modal-container">
            <div class="modal-header">
                <h1>关于新版本测试的重要通知</h1>
            </div>

            <div class="modal-content">
                <div class="intro-text">
                    <p>各位用户，当前发布的新版本为<strong>技术测试版</strong>，主要用于验证新代码、新功能及架构调整。该版本尚处于早期测试阶段，存在较多已知问题，各项功能仍需持续完善。</p>
                </div>

                <div class="info-card suggestions">
                    <div class="card-header">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <h3>特别建议</h3>
                    </div>
                    <ul>
                        <li>八月内请继续以稳定运行的旧版本作为主要使用版本</li>
                        <li>旧版本将持续提供正常更新和维护服务</li>
                        <li>如果您使用新版本一切正常，可以以新版本为主</li>
                        <li>预计九月份彻底完善，完整新版本上线后，旧版本将停止更新维护</li>
                    </ul>
                </div>

                <div class="info-card warnings">
                    <div class="card-header">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <path d="M12 9V13M12 17H12.01M10.29 3.86L1.82 18A2 2 0 003.64 21H20.36A2 2 0 0022.18 18L13.71 3.86A2 2 0 0010.29 3.86Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <h3>新版本体验注意事项</h3>
                    </div>
                    <ul>
                        <li>部分用户可能存在严重影响使用的问题，如：无法入库、无法下载、程序崩溃等</li>
                        <li>卡密激活码在内的后台数据可能不定期重置</li>
                        <li>会员功能暂时无法在新版本使用</li>
                        <li>出现任何问题都很正常，因为目前在早期测试阶段</li>
                    </ul>
                    <div class="critical-warning-container">
                        <div class="critical-warning">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M12 9V13M12 17H12.01M10.29 3.86L1.82 18A2 2 0 003.64 21H20.36A2 2 0 0022.18 18L13.71 3.86A2 2 0 0010.29 3.86Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <span>目前新版本使用出现问题，无售后！</span>
                        </div>
                        <div class="usage-notice">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M13 16H12V12H11M12 8H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <span>新版本软件的工作原理是在你的电脑运行一个本地网页服务，登录成功后不要把软件后台关掉，否则网页功能无法使用</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button id="closeWarningBtn" class="primary-button">
                    <span>我已了解</span>
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(15, 23, 42, 0.85);
            backdrop-filter: blur(12px);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            padding: 20px;
            box-sizing: border-box;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        }

        .modal-container {
            background: #ffffff;
            border-radius: 28px;
            max-width: 680px;
            width: 100%;
            max-height: 90vh;
            overflow: hidden;
            box-shadow:
                0 32px 64px -12px rgba(0, 0, 0, 0.25),
                0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            animation: modalEnter 0.5s cubic-bezier(0.16, 1, 0.3, 1);
            position: relative;
        }

        @keyframes modalEnter {
            from {
                opacity: 0;
                transform: scale(0.92) translateY(24px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .modal-header {
            padding: 40px 40px 32px;
            border-bottom: 1px solid rgba(241, 245, 249, 0.8);
            background: linear-gradient(135deg, #fefbff 0%, #f8fafc 100%);
            position: relative;
            text-align: center;
        }

        .modal-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
        }

        .modal-header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
            color: #0f172a;
            line-height: 1.25;
            letter-spacing: -0.025em;
            background: linear-gradient(135deg, #0f172a 0%, #334155 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .modal-content {
            padding: 0 40px 28px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .intro-text {
            margin-bottom: 32px;
            padding: 24px;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-radius: 20px;
            border: 1px solid rgba(226, 232, 240, 0.6);
        }

        .intro-text p {
            margin: 0;
            font-size: 17px;
            line-height: 1.7;
            color: #334155;
            font-weight: 400;
            letter-spacing: 0.01em;
        }

        .intro-text strong {
            color: #0f172a;
            font-weight: 600;
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .info-card {
            margin-bottom: 24px;
            border-radius: 20px;
            border: 1px solid #e2e8f0;
            overflow: hidden;
            background: #ffffff;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
            transition: all 0.3s ease;
        }

        .info-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
        }

        .suggestions {
            border-color: rgba(219, 234, 254, 0.8);
        }

        .warnings {
            border-color: rgba(254, 215, 170, 0.8);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 14px;
            padding: 24px 28px 18px;
            background: #f8fafc;
            position: relative;
        }

        .suggestions .card-header {
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
            color: #1e40af;
        }

        .warnings .card-header {
            background: linear-gradient(135deg, #fffbeb 0%, #fed7aa 100%);
            color: #d97706;
        }

        .card-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 28px;
            right: 28px;
            height: 1px;
            background: linear-gradient(90deg, transparent, currentColor, transparent);
            opacity: 0.2;
        }

        .card-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            letter-spacing: -0.01em;
        }

        .card-header svg {
            width: 22px;
            height: 22px;
        }

        .info-card ul {
            margin: 0;
            padding: 0 28px 24px;
            list-style: none;
        }

        .info-card li {
            position: relative;
            padding: 12px 0 12px 32px;
            font-size: 16px;
            line-height: 1.6;
            color: #475569;
            font-weight: 400;
            letter-spacing: 0.005em;
            transition: all 0.2s ease;
        }

        .info-card li:hover {
            color: #334155;
            transform: translateX(4px);
        }

        .info-card li::before {
            content: '';
            position: absolute;
            left: 0;
            top: 20px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #94a3b8;
            transition: all 0.2s ease;
        }

        .suggestions li::before {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
        }

        .warnings li::before {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
        }

        .info-card li:hover::before {
            transform: scale(1.2);
        }

        .critical-warning-container {
            margin: 20px 28px 0;
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .critical-warning {
            padding: 20px 24px;
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            border: 2px solid rgba(254, 202, 202, 0.8);
            border-radius: 16px;
            display: flex;
            align-items: center;
            gap: 16px;
            color: #dc2626;
            font-weight: 600;
            font-size: 16px;
            letter-spacing: 0.01em;
            box-shadow: 0 4px 12px rgba(220, 38, 38, 0.1);
            position: relative;
            overflow: hidden;
        }

        .usage-notice {
            padding: 18px 22px;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border: 2px solid rgba(56, 189, 248, 0.3);
            border-radius: 16px;
            display: flex;
            align-items: flex-start;
            gap: 14px;
            color: #0369a1;
            font-weight: 500;
            font-size: 15px;
            line-height: 1.5;
            letter-spacing: 0.01em;
            box-shadow: 0 4px 12px rgba(3, 105, 161, 0.08);
            position: relative;
            overflow: hidden;
        }

        .critical-warning::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #dc2626, #ef4444, #dc2626);
        }

        .usage-notice::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #0369a1, #0ea5e9, #0369a1);
        }

        .critical-warning svg {
            width: 20px;
            height: 20px;
            flex-shrink: 0;
            animation: warningPulse 2s infinite;
        }

        .usage-notice svg {
            width: 18px;
            height: 18px;
            flex-shrink: 0;
            margin-top: 2px;
            animation: infoGlow 3s ease-in-out infinite;
        }

        @keyframes warningPulse {
            0%, 100% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.1);
                opacity: 0.8;
            }
        }

        @keyframes infoGlow {
            0%, 100% {
                transform: scale(1);
                opacity: 0.8;
            }
            50% {
                transform: scale(1.05);
                opacity: 1;
            }
        }

        .modal-footer {
            padding: 32px 40px 40px;
            border-top: 1px solid rgba(241, 245, 249, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            position: relative;
        }

        .modal-footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 40px;
            right: 40px;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
        }



        .primary-button {
            display: flex;
            align-items: center;
            gap: 10px;
            background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
            color: white;
            border: none;
            padding: 14px 28px;
            border-radius: 16px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow:
                0 4px 14px rgba(99, 102, 241, 0.3),
                0 2px 4px rgba(0, 0, 0, 0.1);
            letter-spacing: 0.01em;
            position: relative;
            overflow: hidden;
        }

        .primary-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .primary-button:hover::before {
            left: 100%;
        }

        .primary-button:hover {
            transform: translateY(-2px);
            box-shadow:
                0 8px 20px rgba(99, 102, 241, 0.4),
                0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .primary-button:active {
            transform: translateY(-1px);
        }

        .primary-button svg {
            width: 18px;
            height: 18px;
            transition: transform 0.2s ease;
        }

        .primary-button:hover svg {
            transform: translateX(2px);
        }

        /* 滚动条样式 */
        .modal-content::-webkit-scrollbar {
            width: 6px;
        }

        .modal-content::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }

        .modal-content::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        .modal-content::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .modal-overlay {
                padding: 16px;
            }

            .modal-container {
                border-radius: 24px;
                max-width: 100%;
            }

            .modal-header {
                padding: 32px 28px 24px;
            }

            .modal-header h1 {
                font-size: 24px;
                line-height: 1.3;
            }

            .modal-content {
                padding: 0 28px 24px;
            }

            .intro-text {
                padding: 20px;
                margin-bottom: 28px;
            }

            .intro-text p {
                font-size: 16px;
                line-height: 1.6;
            }

            .info-card ul {
                padding: 0 24px 20px;
            }

            .info-card li {
                font-size: 15px;
                padding: 10px 0 10px 28px;
            }

            .card-header {
                padding: 20px 24px 16px;
            }

            .card-header h3 {
                font-size: 16px;
            }

            .critical-warning-container {
                margin: 16px 24px 0;
                gap: 12px;
            }

            .critical-warning {
                padding: 16px 20px;
                font-size: 15px;
            }

            .usage-notice {
                padding: 14px 18px;
                font-size: 14px;
                gap: 12px;
            }

            .usage-notice svg {
                width: 16px;
                height: 16px;
            }

            .modal-footer {
                padding: 28px 28px 32px;
                flex-direction: column;
                gap: 20px;
                align-items: stretch;
            }



            .primary-button {
                justify-content: center;
                padding: 16px 32px;
                font-size: 16px;
            }
        }

        @media (max-width: 480px) {
            .modal-header h1 {
                font-size: 22px;
            }

            .intro-text p {
                font-size: 15px;
            }

            .info-card li {
                font-size: 14px;
            }

            .card-header h3 {
                font-size: 15px;
            }
        }
    </style>

    <script>
        // 版本警告弹窗逻辑
        document.addEventListener('DOMContentLoaded', function() {
            const modal = document.getElementById('versionWarningModal');
            const closeBtn = document.getElementById('closeWarningBtn');

            // 强制显示弹窗（每次都显示）
            modal.style.display = 'flex';

            // 禁止页面滚动
            document.body.style.overflow = 'hidden';

            // 关闭按钮点击事件
            closeBtn.addEventListener('click', function() {
                // 隐藏弹窗
                modal.style.display = 'none';

                // 恢复页面滚动
                document.body.style.overflow = 'auto';
            });

            // 点击背景关闭弹窗
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeBtn.click();
                }
            });

            // ESC键关闭弹窗
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && modal.style.display === 'flex') {
                    closeBtn.click();
                }
            });
        });
    </script>
</body>
</html>
"""

ERROR_HTML = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>错误</title>
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
    <link rel="icon" type="image/png" href="/static/favicon.png">
    <link rel="shortcut icon" href="/static/favicon.ico">
    <link rel="stylesheet" href="/static/css/特效样式.css">
    <script src="/static/js/安全防护.js"></script>
    <script src="/static/js/特效管理器.js"></script>
    <style>
        :root {
            --primary-bg: #656f8c;
            --text-dark: #333;
            --accent-blue: #3498db;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background-color: var(--primary-bg);
            color: var(--text-dark);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            padding: 20px;
        }
        
        .error-container {
            background-color: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        
        .error-icon {
            font-size: 60px;
            margin-bottom: 20px;
            color: #e74c3c;
        }
        
        h1 {
            color: #e74c3c;
            margin-bottom: 20px;
            font-size: 28px;
        }
        
        p {
            margin: 20px 0;
            color: #666;
            font-size: 16px;
            line-height: 1.6;
        }
        
        a {
            display: inline-block;
            margin-top: 20px;
            padding: 12px 25px;
            background-color: var(--accent-blue);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 500;
            transition: background-color 0.2s, transform 0.2s;
        }
        
        a:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">⚠️</div>
        <h1>出错了</h1>
        <p>{{ error }}</p>
        <a href="/">返回首页</a>
    </div>
</body>
</html>
"""

if __name__ == "__main__":
    # 首先显示登录窗口
    from 登录窗口 import 登录窗口

    print("正在启动...")
    login_window = 登录窗口()
    login_success = login_window.show()

    if not login_success:
        print("登录验证失败或用户取消，程序退出")
        sys.exit(0)

    print("登录验证成功，正在启动主程序...")

    # 获取网络验证客户端实例（用于心跳检测）
    if hasattr(login_window, 'auth_client') and login_window.auth_client:
        auth_client = login_window.auth_client
        auth_manager.set_client(auth_client)  # 设置到管理器中
        print(f"运行中...")

        # 记录程序启动日志
        user_code = auth_client.auth_code if auth_client.is_authenticated() else None
        send_remote_log('info', 'SteamVault Pro 程序启动', {
            'version': '1.0',
            'port': PORT,
            'startup_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'is_packaged': getattr(sys, 'frozen', False)
        }, user_code)
    else:
        print("客户端不可用")

    logger.info(f"服务启动在端口 {PORT}")

    # 在后台线程中执行首次配置检测
    import threading
    config_thread = threading.Thread(target=执行首次配置检测, daemon=True)
    config_thread.start()

    # 检测是否为打包后的环境
    is_packaged = getattr(sys, 'frozen', False)
    # 禁用debug模式以避免重复登录验证问题
    debug_mode = False  # 暂时禁用debug模式

    print(f"SteamVault Pro - Steam 宝库已启动")
    print(f"访问地址: http://127.0.0.1:{PORT}")
    print(f"按 Ctrl+C 停止服务")

    # 启动自动打开浏览器的线程
    browser_thread = threading.Thread(target=自动打开浏览器, daemon=True)
    browser_thread.start()

    try:
        app.run(host="0.0.0.0", port=PORT, debug=debug_mode)
    except KeyboardInterrupt:
        print("\n正在停止服务...")
        # 记录程序关闭日志
        user_code = auth_client.auth_code if auth_client and auth_client.is_authenticated() else None
        send_remote_log('info', 'SteamVault Pro 程序正常关闭', {
            'shutdown_reason': 'user_interrupt',
            'shutdown_time': time.strftime('%Y-%m-%d %H:%M:%S')
        }, user_code)

        # 清理网络验证状态
        if auth_client and auth_client.is_authenticated():
            try:
                print("正在登出...")
                auth_client.logout()
                print("已登出")
            except Exception as e:
                print(f"登出时发生错误: {e}")
        print("服务已停止")
    except Exception as e:
        print(f"服务启动失败: {e}")
        # 记录程序异常关闭日志
        user_code = auth_client.auth_code if auth_client and auth_client.is_authenticated() else None
        send_remote_log('error', 'SteamVault Pro 程序异常关闭', {
            'shutdown_reason': 'exception',
            'error': str(e),
            'shutdown_time': time.strftime('%Y-%m-%d %H:%M:%S')
        }, user_code)

        # 清理网络验证状态
        if auth_client and auth_client.is_authenticated():
            try:
                auth_client.logout()
            except:
                pass
        if is_packaged:
            input("按回车键退出...")
    finally:
        # 确保清理网络验证状态
        if auth_client and auth_client.is_authenticated():
            try:
                auth_client.logout()
            except:
                pass