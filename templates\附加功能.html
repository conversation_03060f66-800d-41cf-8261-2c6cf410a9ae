<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SteamVault Pro - Steam 宝库 - 附加功能</title>
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
    <link rel="icon" type="image/png" href="/static/favicon.png">
    <link rel="shortcut icon" href="/static/favicon.ico">
    <link rel="stylesheet" href="/static/css/特效样式.css">
    <script src="/static/js/安全防护.js"></script>
    <script src="/static/js/特效管理器.js"></script>
    <style>
        :root {
            --primary-bg: #656f8c;
            --sidebar-bg: #373e54;
            --card-bg: #eef1f8;
            --card-purple: #f7eeff;
            --card-blue: #edf5ff;
            --text-dark: #333;
            --text-light: #666;
            --accent-green: #4CAF50;
            --accent-purple: #8e44ad;
            --accent-blue: #3498db;
            --border-radius: 12px;
            --card-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background-color: var(--primary-bg);
            color: var(--text-dark);
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            background: linear-gradient(135deg, var(--sidebar-bg) 0%, #2c3e50 100%);
            width: 85px;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 25px 0;
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.15);
            position: relative;
            overflow: hidden;
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            pointer-events: none;
        }

        .sidebar-nav {
            display: flex;
            flex-direction: column;
            gap: 12px;
            width: 100%;
            align-items: center;
            padding-top: 10px;
        }

        .sidebar-icon {
            width: 55px;
            height: 55px;
            border-radius: 18px;
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            margin-bottom: 5px;
        }

        .sidebar-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
        }

        .sidebar-icon:hover::before {
            left: 100%;
        }

        .sidebar-icon-symbol {
            font-size: 22px;
            margin-bottom: 2px;
            transition: all 0.3s ease;
        }

        .sidebar-icon-text {
            font-size: 10px;
            font-weight: 500;
            opacity: 0.9;
            transition: all 0.3s ease;
        }

        .sidebar-icon:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.2);
            color: white;
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .sidebar-icon:hover .sidebar-icon-symbol {
            transform: scale(1.1);
        }

        .sidebar-icon.active {
            background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
            border-color: var(--accent-blue);
            color: white;
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
            transform: translateY(-1px);
        }

        .sidebar-icon.active::after {
            content: '';
            position: absolute;
            right: -3px;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 30px;
            background: linear-gradient(to bottom, var(--accent-blue), var(--accent-purple));
            border-radius: 2px;
            box-shadow: 0 0 10px rgba(52, 152, 219, 0.5);
        }

        .sidebar-icon.active .sidebar-icon-symbol {
            transform: scale(1.1);
        }
        
        .main-container {
            flex: 1;
            padding: 30px;
            display: flex;
            flex-direction: column;
            overflow-x: hidden;
        }
        
        .header {
            display: flex;
            flex-direction: column;
            margin-bottom: 30px;
        }
        
        .page-title {
            background-color: var(--card-blue);
            border-radius: var(--border-radius);
            padding: 25px;
            box-shadow: var(--card-shadow);
            margin-bottom: 20px;
        }
        
        .page-title h1 {
            font-size: 26px;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 5px;
        }
        
        .page-title p {
            font-size: 15px;
            color: var(--text-light);
        }
        
        .features-container {
            background-color: white;
            border-radius: var(--border-radius);
            padding: 30px;
            box-shadow: var(--card-shadow);
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .feature-button {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 15px 20px;
            font-size: 14px;
            font-weight: 500;
            color: var(--text-dark);
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .feature-button:hover {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-color: var(--accent-blue);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.2);
        }

        .feature-button:active {
            transform: translateY(0);
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
        }

        .feature-button.disabled {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-color: #dee2e6;
            color: #6c757d;
            cursor: not-allowed;
            opacity: 0.6;
        }

        .feature-button.disabled:hover {
            transform: none;
            box-shadow: none;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-color: #dee2e6;
        }

        .feature-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s;
        }

        .feature-button:hover::before {
            left: 100%;
        }

        .feature-button.disabled::before {
            display: none;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-dark);
            margin: 25px 0 15px 0;
            padding-bottom: 8px;
            border-bottom: 2px solid var(--accent-blue);
            display: inline-block;
        }

        .section-title:first-child {
            margin-top: 0;
        }

        /* 通知系统样式优化 */
        #notification-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            pointer-events: none;
            max-width: 400px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .notification {
            pointer-events: auto;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .notification::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .notification:hover::before {
            left: 100%;
        }

        .notification:hover {
            transform: translateX(-5px) scale(1.02);
            box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-container {
                margin-left: 0;
                padding: 20px;
            }

            .features-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .feature-button {
                padding: 12px 20px;
                font-size: 14px;
            }

            /* 移动端通知样式 */
            #notification-container {
                top: 15px;
                left: 15px;
                right: 15px;
                max-width: none;
                gap: 10px;
            }

            .notification {
                font-size: 14px;
                padding: 14px 16px !important;
                border-radius: 10px !important;
            }
        }

        @media (max-width: 480px) {
            .main-container {
                padding: 15px;
            }

            .features-grid {
                gap: 12px;
            }

            .feature-button {
                padding: 10px 16px;
                font-size: 13px;
                min-height: 50px;
            }

            .section-title {
                font-size: 16px;
            }

            /* 小屏幕通知样式 */
            #notification-container {
                top: 10px;
                left: 10px;
                right: 10px;
                gap: 8px;
            }

            .notification {
                font-size: 13px;
                padding: 12px 14px !important;
                border-radius: 8px !important;
            }
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <!-- 导航菜单 -->
        <nav class="sidebar-nav">
            <a href="/" class="sidebar-icon" title="首页 - 浏览游戏列表">
                <div class="sidebar-icon-symbol">🏠</div>
                <div class="sidebar-icon-text">首页</div>
            </a>

            <a href="/管理" class="sidebar-icon" title="管理页面 - 游戏管理">
                <div class="sidebar-icon-symbol">📊</div>
                <div class="sidebar-icon-text">管理</div>
            </a>

            <a href="/附加功能" class="sidebar-icon active" title="附加功能 - 扩展工具">
                <div class="sidebar-icon-symbol">🔧</div>
                <div class="sidebar-icon-text">附加功能</div>
            </a>

            <a href="/设置" class="sidebar-icon" title="设置 - 系统配置">
                <div class="sidebar-icon-symbol">⚙️</div>
                <div class="sidebar-icon-text">设置</div>
            </a>

            <a href="https://wish.steamlab.cc/" class="sidebar-icon" title="许愿 - 游戏许愿" target="_blank">
                <div class="sidebar-icon-symbol">🌟</div>
                <div class="sidebar-icon-text">许愿</div>
            </a>

            <a href="https://help.steamlab.cc/%E4%B8%BB%E9%A1%B5.html" class="sidebar-icon" title="会员申请 - 申请会员服务" target="_blank">
                <div class="sidebar-icon-symbol">👑</div>
                <div class="sidebar-icon-text">会员申请</div>
            </a>

            <a href="https://help.steamlab.cc/%E5%94%AE%E5%90%8E%E5%B7%A5%E5%8D%95.html" class="sidebar-icon" title="售后工单 - 提交售后服务" target="_blank">
                <div class="sidebar-icon-symbol">🎫</div>
                <div class="sidebar-icon-text">售后工单</div>
            </a>
        </nav>
    </div>
    
    <div class="main-container">
        <div class="header">
            <div class="page-title">
                <h1>附加功能</h1>
                <p>SteamVault Pro - Steam 宝库的附加功能</p>
            </div>
        </div>
        
        <div class="features-container">
            <!-- Steam相关功能 -->
            <div class="section-title">Steam 相关功能</div>
            <div class="features-grid">
                <button class="feature-button" data-feature="start-steam">
                    启动Steam
                </button>
                <button class="feature-button" data-feature="close-steam">
                    结束Steam
                </button>
                <button class="feature-button" data-feature="install-steam">
                    安装Steam
                </button>
                <button class="feature-button" data-feature="open-steam-website">
                    打开Steam官网
                </button>
                <button class="feature-button" data-feature="uninstall-steam">
                    彻底卸载Steam
                </button>
                <button class="feature-button" data-feature="open-steam-config">
                    打开Steam账户配置目录
                </button>
                <button class="feature-button" data-feature="steam-restore">
                    一键还原Steam【恢复到未入库状态】
                </button>
            </div>

            <!-- 正式会员功能 -->
            <div class="section-title">正式会员功能</div>
            <div class="features-grid">
                <button class="feature-button" data-feature="vip-game-box">
                    正式会员_单机游戏盒子_满速下载 包含部分D加密和Switch游戏
                </button>
                <button class="feature-button disabled" data-feature="vip-dlc-tool">
                    正式会员_全DLC破解工具
                </button>
                <button class="feature-button" data-feature="vip-game-mod">
                    正式会员_全单机游戏修改器下载可破解WeMod
                </button>
                <button class="feature-button" data-feature="vip-achievement-tool">
                    正式会员_游戏成就解锁工具
                </button>
                <button class="feature-button" data-feature="vip-dlc-unlock">
                    正式会员_DLC解锁器_通用
                </button>
                <button class="feature-button" data-feature="vip-d-encryption">
                    正式会员_D加密弹窗报错解决办法·D加密授权·脱机脱壳
                </button>
                <button class="feature-button" data-feature="vip-error-fix">
                    正式会员_启动游戏 报错错误代码结尾是 6:000065432 的解决办法
                </button>
            </div>

            <!-- 其他功能 -->
            <div class="section-title">其他功能</div>
            <div class="features-grid">
                <button class="feature-button" data-feature="tutorial">
                    联机教程
                </button>

                <button class="feature-button disabled" data-feature="steam-monitor">
                    Steam家庭监护PIN码破解
                </button>
                <button class="feature-button" data-feature="free-games">
                    8000G免费单机游戏下载打包
                </button>
                <button class="feature-button" data-feature="multi-platform">
                    过第三方平台游戏工具【有育碧R星等平台】
                </button>
            </div>
        </div>
    </div>

    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initFeatureButtons();
        });

        // 初始化功能按钮
        function initFeatureButtons() {
            const buttons = document.querySelectorAll('.feature-button');

            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    if (this.classList.contains('disabled')) {
                        showNotification('该功能暂未开放', 'info');
                        return;
                    }

                    const feature = this.getAttribute('data-feature');
                    handleFeatureClick(feature);
                });
            });
        }

        // 检查VIP状态
        async function checkVipStatus() {
            try {
                const response = await fetch('/api/check-vip-status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const result = await response.json();
                return result.is_vip || false;
            } catch (error) {
                console.error('检查VIP状态失败:', error);
                return false;
            }
        }

        // 处理功能按钮点击
        async function handleFeatureClick(feature) {
            console.log('点击功能:', feature);

            // 检查是否为VIP功能
            const vipFeatures = [
                'vip-game-box',
                'vip-game-mod',
                'vip-achievement-tool',
                'vip-dlc-unlock',
                'vip-d-encryption',
                'vip-error-fix',
                'free-games',
                'multi-platform',
                'tutorial'
            ];

            // 如果是VIP功能，先检查用户状态
            if (vipFeatures.includes(feature)) {
                showNotification('正在验证会员状态...', 'info');
                const isVip = await checkVipStatus();

                if (!isVip) {
                    showNotification('此功能仅限正式会员使用，请在左侧自行申请会员服务', 'warning');
                    return;
                }
            }

            // Steam相关功能
            switch(feature) {
                case 'start-steam':
                    startSteam();
                    break;
                case 'close-steam':
                    stopSteam();
                    break;
                case 'install-steam':
                    installSteam();
                    break;
                case 'open-steam-website':
                    openSteamWebsite();
                    break;
                case 'uninstall-steam':
                    uninstallSteam();
                    break;
                case 'open-steam-config':
                    openSteamConfig();
                    break;
                case 'steam-restore':
                    restoreSteam();
                    break;
                // 正式会员功能
                case 'vip-game-box':
                    openVipGameBox();
                    break;
                case 'vip-game-mod':
                    openVipGameMod();
                    break;
                case 'vip-achievement-tool':
                    openVipAchievementTool();
                    break;
                case 'vip-dlc-unlock':
                    openVipDlcUnlock();
                    break;
                case 'vip-d-encryption':
                    openVipDEncryption();
                    break;
                case 'vip-error-fix':
                    openVipErrorFix();
                    break;
                // 其他功能
                case 'tutorial':
                    openTutorial();
                    break;
                case 'free-games':
                    openFreeGames();
                    break;
                case 'multi-platform':
                    openMultiPlatform();
                    break;
                // 其他功能暂未实现
                default:
                    showNotification('功能开发中，敬请期待', 'info');
                    console.log('未知功能:', feature);
            }
        }

        // Steam功能实现
        async function startSteam() {
            try {
                showNotification('正在启动Steam...', 'info');
                const response = await fetch('/api/steam/start', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const result = await response.json();

                if (result.status === 'success') {
                    showNotification(result.message, 'success');
                } else if (result.status === 'warning') {
                    showNotification(result.message, 'warning');
                } else {
                    showNotification(result.message, 'error');
                }
            } catch (error) {
                showNotification('启动Steam失败', 'error');
                console.error('启动Steam错误:', error);
            }
        }

        async function stopSteam() {
            try {
                showNotification('正在结束Steam进程...', 'info');
                const response = await fetch('/api/steam/stop', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const result = await response.json();

                if (result.status === 'success') {
                    showNotification(result.message, 'success');
                } else if (result.status === 'warning') {
                    showNotification(result.message, 'warning');
                } else if (result.status === 'info') {
                    showNotification(result.message, 'info');
                } else {
                    showNotification(result.message, 'error');
                }
            } catch (error) {
                showNotification('结束Steam失败', 'error');
                console.error('结束Steam错误:', error);
            }
        }

        async function installSteam() {
            try {
                showNotification('正在启动Steam安装包...', 'info');
                const response = await fetch('/api/steam/install', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const result = await response.json();

                if (result.status === 'success') {
                    showNotification(result.message, 'success');
                } else {
                    showNotification(result.message, 'error');
                }
            } catch (error) {
                showNotification('启动Steam安装包失败', 'error');
                console.error('安装Steam错误:', error);
            }
        }

        async function openSteamWebsite() {
            try {
                showNotification('正在打开Steam官网...', 'info');
                const response = await fetch('/api/steam/open-website', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const result = await response.json();

                if (result.status === 'success') {
                    showNotification(result.message, 'success');
                } else {
                    showNotification(result.message, 'error');
                }
            } catch (error) {
                showNotification('打开Steam官网失败', 'error');
                console.error('打开Steam官网错误:', error);
            }
        }

        async function uninstallSteam() {
            try {
                showNotification('正在启动Geek Uninstaller...', 'info');
                const response = await fetch('/api/steam/uninstall', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const result = await response.json();

                if (result.status === 'success') {
                    showNotification(result.message, 'success');
                } else {
                    showNotification(result.message, 'error');
                }
            } catch (error) {
                showNotification('启动Geek Uninstaller失败', 'error');
                console.error('彻底卸载Steam错误:', error);
            }
        }

        async function openSteamConfig() {
            try {
                showNotification('正在打开Steam配置目录...', 'info');
                const response = await fetch('/api/steam/open-config', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const result = await response.json();

                if (result.status === 'success') {
                    showNotification(result.message, 'success');
                } else {
                    showNotification(result.message, 'error');
                }
            } catch (error) {
                showNotification('打开Steam配置目录失败', 'error');
                console.error('打开Steam配置目录错误:', error);
            }
        }

        async function restoreSteam() {
            try {
                // 直接执行，不显示任何提示
                const response = await fetch('/api/steam/restore', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const result = await response.json();

                // 只显示简单的完成消息，不透露具体操作
                if (result.status === 'success' || result.status === 'warning') {
                    showNotification('操作完成', 'success');
                } else {
                    showNotification('操作失败', 'error');
                }
            } catch (error) {
                showNotification('操作失败', 'error');
            }
        }

        // 正式会员功能实现（已通过VIP验证）
        async function openVipGameBox() {
            try {
                showNotification('正在打开单机游戏盒子...', 'info');
                window.open('https://share.weiyun.com/UfmcHBgE', '_blank');
                showNotification('已在新窗口打开', 'success');
            } catch (error) {
                showNotification('打开失败', 'error');
                console.error('打开单机游戏盒子错误:', error);
            }
        }

        async function openVipGameMod() {
            try {
                showNotification('正在打开游戏修改器下载...', 'info');
                window.open('https://share.weiyun.com/QvhdZvKw', '_blank');
                showNotification('已在新窗口打开', 'success');
            } catch (error) {
                showNotification('打开失败', 'error');
                console.error('打开游戏修改器错误:', error);
            }
        }

        async function openVipAchievementTool() {
            try {
                showNotification('正在打开成就解锁工具...', 'info');
                window.open('https://share.weiyun.com/Y2SoaPaQ', '_blank');
                showNotification('已在新窗口打开', 'success');
            } catch (error) {
                showNotification('打开失败', 'error');
                console.error('打开成就解锁工具错误:', error);
            }
        }

        async function openVipDlcUnlock() {
            try {
                showNotification('正在打开DLC解锁器...', 'info');
                window.open('https://www.123pan.com/s/PaPbVv-LpbgH.html', '_blank');
                showNotification('已在新窗口打开', 'success');
            } catch (error) {
                showNotification('打开失败', 'error');
                console.error('打开DLC解锁器错误:', error);
            }
        }

        async function openVipDEncryption() {
            try {
                showNotification('正在打开D加密解决方案...', 'info');
                window.open('https://www.yuque.com/xiangjiaonigebunana-9d4l7/kb/ix59o5f9s5ilaq1i?singleDoc#', '_blank');
                showNotification('已在新窗口打开', 'success');
            } catch (error) {
                showNotification('打开失败', 'error');
                console.error('打开D加密解决方案错误:', error);
            }
        }

        async function openVipErrorFix() {
            try {
                showNotification('正在打开错误代码解决方案...', 'info');
                window.open('https://share.weiyun.com/q5AP7vrB', '_blank');
                showNotification('已在新窗口打开', 'success');
            } catch (error) {
                showNotification('打开失败', 'error');
                console.error('打开错误代码解决方案错误:', error);
            }
        }

        // 其他功能实现
        async function openTutorial() {
            try {
                showNotification('正在打开联机教程...', 'info');
                window.open('https://www.yuque.com/xiangjiaonigebunana-9d4l7/kb/btyt79un7ksgqgi3?singleDoc#', '_blank');
                showNotification('已在新窗口打开', 'success');
            } catch (error) {
                showNotification('打开失败', 'error');
                console.error('打开联机教程错误:', error);
            }
        }

        async function openFreeGames() {
            try {
                showNotification('正在打开免费游戏下载...', 'info');
                window.open('https://share.weiyun.com/7lNiXzOD', '_blank');
                showNotification('已在新窗口打开', 'success');
            } catch (error) {
                showNotification('打开失败', 'error');
                console.error('打开免费游戏下载错误:', error);
            }
        }

        async function openMultiPlatform() {
            try {
                showNotification('正在打开第三方平台游戏工具...', 'info');
                window.open('https://share.weiyun.com/HbqnvPsW', '_blank');
                showNotification('已在新窗口打开', 'success');
            } catch (error) {
                showNotification('打开失败', 'error');
                console.error('打开第三方平台游戏工具错误:', error);
            }
        }

        // 通知系统管理器
        const NotificationManager = {
            container: null,
            notifications: [],

            // 初始化通知容器
            init() {
                if (!this.container) {
                    this.container = document.createElement('div');
                    this.container.id = 'notification-container';
                    this.container.style.cssText = `
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        z-index: 10000;
                        pointer-events: none;
                        max-width: 400px;
                        display: flex;
                        flex-direction: column;
                        gap: 10px;
                    `;
                    document.body.appendChild(this.container);
                }
            },

            // 添加通知
            add(message, type = 'info') {
                this.init();

                const notification = document.createElement('div');
                notification.className = `notification notification-${type}`;

                // 设置基础样式
                notification.style.cssText = `
                    padding: 12px 20px;
                    border-radius: 12px;
                    color: white;
                    font-weight: 500;
                    font-size: 14px;
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(255, 255, 255, 0.1);
                    pointer-events: auto;
                    cursor: pointer;
                    transform: translateX(100%) scale(0.9);
                    opacity: 0;
                    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                    max-width: 100%;
                    word-wrap: break-word;
                    position: relative;
                    overflow: hidden;
                `;

                // 根据类型设置背景色和图标
                let icon = '';
                switch(type) {
                    case 'success':
                        notification.style.background = 'linear-gradient(135deg, #4CAF50, #45a049)';
                        icon = '✓';
                        break;
                    case 'error':
                        notification.style.background = 'linear-gradient(135deg, #f44336, #da190b)';
                        icon = '✕';
                        break;
                    case 'warning':
                        notification.style.background = 'linear-gradient(135deg, #ff9800, #f57c00)';
                        icon = '⚠';
                        break;
                    default:
                        notification.style.background = 'linear-gradient(135deg, #2196F3, #1976D2)';
                        icon = 'ℹ';
                }

                // 设置内容
                notification.innerHTML = `
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <span style="font-size: 16px; flex-shrink: 0;">${icon}</span>
                        <span style="flex: 1;">${message}</span>
                        <span style="font-size: 12px; opacity: 0.7; cursor: pointer; padding: 2px 6px; border-radius: 4px; background: rgba(255,255,255,0.2);" onclick="NotificationManager.remove(this.closest('.notification'))">×</span>
                    </div>
                `;

                // 添加到容器
                this.container.appendChild(notification);
                this.notifications.push(notification);

                // 进入动画
                requestAnimationFrame(() => {
                    notification.style.transform = 'translateX(0) scale(1)';
                    notification.style.opacity = '1';
                });

                // 自动移除
                setTimeout(() => {
                    this.remove(notification);
                }, 4000);

                // 点击移除
                notification.addEventListener('click', () => {
                    this.remove(notification);
                });

                return notification;
            },

            // 移除通知
            remove(notification) {
                if (!notification || !notification.parentNode) return;

                // 退出动画
                notification.style.transform = 'translateX(100%) scale(0.9)';
                notification.style.opacity = '0';
                notification.style.maxHeight = notification.offsetHeight + 'px';

                setTimeout(() => {
                    notification.style.maxHeight = '0';
                    notification.style.padding = '0 20px';
                    notification.style.margin = '0';
                }, 200);

                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                        const index = this.notifications.indexOf(notification);
                        if (index > -1) {
                            this.notifications.splice(index, 1);
                        }
                    }
                }, 400);
            },

            // 清除所有通知
            clear() {
                this.notifications.forEach(notification => {
                    this.remove(notification);
                });
            }
        };

        // 显示通知消息（统一接口）
        function showNotification(message, type = 'info') {
            return NotificationManager.add(message, type);
        }

        // 响应式设计适配
        function updateNotificationPosition() {
            if (NotificationManager.container) {
                const isMobile = window.innerWidth <= 768;
                const isSmallMobile = window.innerWidth <= 480;

                if (isSmallMobile) {
                    NotificationManager.container.style.cssText = `
                        position: fixed;
                        top: 10px;
                        left: 10px;
                        right: 10px;
                        z-index: 10000;
                        pointer-events: none;
                        max-width: none;
                        display: flex;
                        flex-direction: column;
                        gap: 8px;
                    `;
                } else if (isMobile) {
                    NotificationManager.container.style.cssText = `
                        position: fixed;
                        top: 15px;
                        left: 15px;
                        right: 15px;
                        z-index: 10000;
                        pointer-events: none;
                        max-width: none;
                        display: flex;
                        flex-direction: column;
                        gap: 10px;
                    `;
                } else {
                    NotificationManager.container.style.cssText = `
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        z-index: 10000;
                        pointer-events: none;
                        max-width: 400px;
                        display: flex;
                        flex-direction: column;
                        gap: 10px;
                    `;
                }
            }
        }

        // 监听窗口大小变化
        window.addEventListener('resize', updateNotificationPosition);
    </script>
</body>
</html>