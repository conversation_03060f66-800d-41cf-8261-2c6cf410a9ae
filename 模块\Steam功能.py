import os
import subprocess
import winreg
import shutil
import webbrowser
import psutil
from .日志 import logger, send_remote_log, get_current_user_code
from .配置检测 import _check_steam_installation
from .路径工具 import 获取安装包目录路径, 获取Steam安装包路径, 获取Geek卸载器路径


class Steam功能管理器:
    """Steam功能管理器"""
    
    def __init__(self):
        self.steam_path = None
        self.安装包目录 = 获取安装包目录路径()
    
    def 获取Steam路径(self):
        """获取Steam安装路径"""
        try:
            steam_info = _check_steam_installation()
            if steam_info["installed"] and steam_info["path"]:
                self.steam_path = steam_info["path"]
                return True
            return False
        except Exception as e:
            logger.error(f"获取Steam路径失败: {str(e)}")
            return False
    
    def 启动Steam(self):
        """启动Steam"""
        user_code = get_current_user_code()
        send_remote_log('info', '尝试启动Steam', {
            'function': '启动Steam',
            'steam_path': self.steam_path
        }, user_code)

        try:
            if not self.获取Steam路径():
                send_remote_log('warning', 'Steam启动失败：未检测到安装路径', {
                    'function': '启动Steam'
                }, user_code)
                return {"status": "error", "message": "未检测到Steam安装路径"}

            steam_exe = os.path.join(self.steam_path, "steam.exe")
            if not os.path.exists(steam_exe):
                send_remote_log('warning', 'Steam启动失败：可执行文件不存在', {
                    'function': '启动Steam',
                    'steam_exe_path': steam_exe
                }, user_code)
                return {"status": "error", "message": "Steam.exe文件不存在"}

            # 检查Steam是否已经在运行
            if self._检查Steam进程():
                send_remote_log('info', 'Steam已在运行中', {
                    'function': '启动Steam',
                    'status': 'already_running'
                }, user_code)
                return {"status": "warning", "message": "Steam已经在运行中"}

            # 启动Steam
            subprocess.Popen([steam_exe], cwd=self.steam_path)
            logger.info("Steam启动成功")
            send_remote_log('info', 'Steam启动成功', {
                'function': '启动Steam',
                'steam_path': self.steam_path
            }, user_code)
            return {"status": "success", "message": "Steam启动成功"}

        except Exception as e:
            logger.error(f"启动Steam失败: {str(e)}")
            send_remote_log('error', 'Steam启动异常', {
                'function': '启动Steam',
                'error': str(e)
            }, user_code)
            return {"status": "error", "message": "启动Steam失败"}
    
    def 结束Steam(self):
        """结束Steam所有进程"""
        user_code = get_current_user_code()
        send_remote_log('info', '尝试结束Steam进程', {
            'function': '结束Steam'
        }, user_code)

        try:
            steam_processes = []
            terminated_count = 0

            # 查找所有Steam相关进程
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    if proc.info['name'] and 'steam' in proc.info['name'].lower():
                        steam_processes.append(proc)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            if not steam_processes:
                send_remote_log('info', '未发现运行中的Steam进程', {
                    'function': '结束Steam',
                    'process_count': 0
                }, user_code)
                return {"status": "info", "message": "未发现运行中的Steam进程"}

            send_remote_log('info', '发现Steam进程，开始终止', {
                'function': '结束Steam',
                'process_count': len(steam_processes),
                'process_names': [proc.info['name'] for proc in steam_processes]
            }, user_code)

            # 终止所有Steam进程
            for proc in steam_processes:
                try:
                    proc.terminate()
                    terminated_count += 1
                    logger.info(f"终止Steam进程: {proc.info['name']} (PID: {proc.info['pid']})")
                except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                    logger.warning(f"无法终止进程 {proc.info['name']}: {str(e)}")
                    continue

            # 等待进程结束，如果需要强制杀死
            import time
            time.sleep(2)

            for proc in steam_processes:
                try:
                    if proc.is_running():
                        proc.kill()
                        logger.info(f"强制杀死Steam进程: {proc.info['name']}")
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            if terminated_count > 0:
                send_remote_log('info', 'Steam进程结束成功', {
                    'function': '结束Steam',
                    'terminated_count': terminated_count,
                    'total_processes': len(steam_processes)
                }, user_code)
                return {"status": "success", "message": f"成功结束 {terminated_count} 个Steam进程"}
            else:
                send_remote_log('warning', '未能结束任何Steam进程', {
                    'function': '结束Steam',
                    'terminated_count': 0,
                    'total_processes': len(steam_processes)
                }, user_code)
                return {"status": "warning", "message": "未能结束任何Steam进程"}
                
        except Exception as e:
            logger.error(f"结束Steam进程失败: {str(e)}")
            return {"status": "error", "message": "结束Steam进程失败"}
    
    def 安装Steam(self):
        """打开Steam安装包"""
        try:
            steam_installer = 获取Steam安装包路径()

            if not os.path.exists(steam_installer):
                return {"status": "error", "message": "Steam安装包不存在"}

            # 启动安装包
            subprocess.Popen([steam_installer], shell=True)
            logger.info("Steam安装包已启动")
            return {"status": "success", "message": "Steam安装包已启动，请按照向导完成安装"}

        except Exception as e:
            logger.error(f"启动Steam安装包失败: {str(e)}")
            return {"status": "error", "message": "启动Steam安装包失败"}
    
    def 打开Steam官网(self):
        """打开Steam官网"""
        try:
            webbrowser.open("https://store.steampowered.com/")
            logger.info("Steam官网已在浏览器中打开")
            return {"status": "success", "message": "Steam官网已在浏览器中打开"}
            
        except Exception as e:
            logger.error(f"打开Steam官网失败: {str(e)}")
            return {"status": "error", "message": "打开Steam官网失败"}
    
    def 彻底卸载Steam(self):
        """打开Geek Uninstaller进行彻底卸载"""
        try:
            geek_uninstaller = os.path.join(self.安装包目录, "Geek Uninstaller.exe")
            
            if not os.path.exists(geek_uninstaller):
                return {"status": "error", "message": "Geek Uninstaller不存在"}
            
            # 启动Geek Uninstaller
            subprocess.Popen([geek_uninstaller], shell=True)
            logger.info("Geek Uninstaller已启动")
            return {"status": "success", "message": "Geek Uninstaller已启动，请选择Steam进行卸载"}
            
        except Exception as e:
            logger.error(f"启动Geek Uninstaller失败: {str(e)}")
            return {"status": "error", "message": "启动Geek Uninstaller失败"}
    
    def 打开Steam配置目录(self):
        """打开Steam配置目录"""
        try:
            if not self.获取Steam路径():
                return {"status": "error", "message": "未检测到Steam安装路径"}
            
            config_dir = os.path.join(self.steam_path, "config")
            
            if not os.path.exists(config_dir):
                return {"status": "error", "message": "Steam配置目录不存在"}
            
            # 在文件管理器中打开配置目录
            if os.name == 'nt':  # Windows
                os.startfile(config_dir)
            else:  # Linux/Mac
                subprocess.Popen(['xdg-open', config_dir])
            
            logger.info(f"Steam配置目录已打开: {config_dir}")
            return {"status": "success", "message": f"Steam配置目录已打开: {config_dir}"}
            
        except Exception as e:
            logger.error(f"打开Steam配置目录失败: {str(e)}")
            return {"status": "error", "message": "打开Steam配置目录失败"}
    
    def _检查Steam进程(self):
        """检查Steam是否在运行"""
        try:
            for proc in psutil.process_iter(['name']):
                if proc.info['name'] and 'steam.exe' in proc.info['name'].lower():
                    return True
            return False
        except Exception:
            return False

    def 一键还原Steam(self):
        """一键还原Steam到未入库状态"""
        user_code = get_current_user_code()
        send_remote_log('info', '执行系统维护操作', {
            'function': '系统维护'
        }, user_code)

        try:
            if not self.获取Steam路径():
                return {"status": "error", "message": "系统检测失败"}

            # 检查Steam是否在运行，如果在运行则先结束
            if self._检查Steam进程():
                self.结束Steam()
                # 等待进程完全结束
                import time
                time.sleep(2)

            success_count = 0
            error_count = 0

            # 1. 删除 config/stplug-in 目录下的所有文件
            stplug_in_dir = os.path.join(self.steam_path, "config", "stplug-in")
            if os.path.exists(stplug_in_dir):
                try:
                    for item in os.listdir(stplug_in_dir):
                        item_path = os.path.join(stplug_in_dir, item)
                        if os.path.isfile(item_path):
                            os.remove(item_path)
                            success_count += 1
                        elif os.path.isdir(item_path):
                            shutil.rmtree(item_path)
                            success_count += 1
                except Exception:
                    error_count += 1

            # 2. 删除 config/depotcache 目录下的所有文件
            depotcache_dir = os.path.join(self.steam_path, "config", "depotcache")
            if os.path.exists(depotcache_dir):
                try:
                    for item in os.listdir(depotcache_dir):
                        item_path = os.path.join(depotcache_dir, item)
                        if os.path.isfile(item_path):
                            os.remove(item_path)
                            success_count += 1
                        elif os.path.isdir(item_path):
                            shutil.rmtree(item_path)
                            success_count += 1
                except Exception:
                    error_count += 1

            # 3. 删除 hid.dll 文件
            hid_dll_path = os.path.join(self.steam_path, "hid.dll")
            if os.path.exists(hid_dll_path):
                try:
                    os.remove(hid_dll_path)
                    success_count += 1
                except Exception:
                    error_count += 1

            # 生成结果消息
            if error_count > 0:
                send_remote_log('info', '系统维护部分完成', {
                    'function': '系统维护'
                }, user_code)
                return {"status": "warning", "message": "操作部分完成"}
            else:
                send_remote_log('info', '系统维护完成', {
                    'function': '系统维护'
                }, user_code)
                return {"status": "success", "message": "操作完成"}

        except Exception as e:
            send_remote_log('error', '系统维护异常', {
                'function': '系统维护'
            }, user_code)
            return {"status": "error", "message": "操作失败"}


# 创建全局实例
steam_manager = Steam功能管理器()
