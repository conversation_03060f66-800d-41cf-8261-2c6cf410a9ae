/**
 * 统一通知系统管理器
 * 支持多种通知类型、堆叠显示、响应式设计和动画效果
 */
class 通知系统管理器 {
    constructor() {
        this.container = null;
        this.notifications = [];
        this.maxNotifications = 5; // 最大同时显示的通知数量
        this.defaultDuration = 4000; // 默认显示时长
        this.init();
    }

    // 初始化通知容器
    init() {
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.id = 'notification-container';
            this.container.className = 'notification-container';
            this.updateContainerStyle();
            document.body.appendChild(this.container);
            
            // 监听窗口大小变化
            window.addEventListener('resize', () => this.updateContainerStyle());
        }
    }

    // 更新容器样式（响应式设计）
    updateContainerStyle() {
        if (!this.container) return;
        
        const isMobile = window.innerWidth <= 768;
        const isSmallMobile = window.innerWidth <= 480;
        
        if (isSmallMobile) {
            this.container.style.cssText = `
                position: fixed;
                top: 10px;
                left: 10px;
                right: 10px;
                z-index: 10000;
                pointer-events: none;
                max-width: none;
                display: flex;
                flex-direction: column;
                gap: 8px;
            `;
        } else if (isMobile) {
            this.container.style.cssText = `
                position: fixed;
                top: 15px;
                left: 15px;
                right: 15px;
                z-index: 10000;
                pointer-events: none;
                max-width: none;
                display: flex;
                flex-direction: column;
                gap: 10px;
            `;
        } else {
            this.container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                pointer-events: none;
                max-width: 400px;
                display: flex;
                flex-direction: column;
                gap: 10px;
            `;
        }
    }

    // 添加通知
    add(message, type = 'info', duration = null) {
        this.init();
        
        // 如果通知数量超过最大值，移除最旧的通知
        if (this.notifications.length >= this.maxNotifications) {
            this.remove(this.notifications[0]);
        }
        
        const notification = this.createNotification(message, type);
        this.container.appendChild(notification);
        this.notifications.push(notification);
        
        // 进入动画
        requestAnimationFrame(() => {
            notification.style.transform = 'translateX(0) scale(1)';
            notification.style.opacity = '1';
        });
        
        // 自动移除
        const autoRemoveDelay = duration || this.defaultDuration;
        setTimeout(() => {
            this.remove(notification);
        }, autoRemoveDelay);
        
        return notification;
    }

    // 创建通知元素
    createNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        
        // 获取类型配置
        const config = this.getTypeConfig(type);
        const isMobile = window.innerWidth <= 768;
        const isSmallMobile = window.innerWidth <= 480;
        
        // 设置基础样式
        notification.style.cssText = `
            padding: ${isSmallMobile ? '12px 14px' : isMobile ? '14px 16px' : '15px 20px'};
            border-radius: ${isSmallMobile ? '8px' : isMobile ? '10px' : '12px'};
            color: white;
            font-weight: 500;
            font-size: ${isSmallMobile ? '13px' : isMobile ? '14px' : '14px'};
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            pointer-events: auto;
            cursor: pointer;
            transform: translateX(100%) scale(0.9);
            opacity: 0;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            max-width: 100%;
            word-wrap: break-word;
            position: relative;
            overflow: hidden;
            background: ${config.background};
        `;
        
        // 设置内容
        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <span style="font-size: 16px; flex-shrink: 0;">${config.icon}</span>
                <span style="flex: 1;">${message}</span>
                <span style="font-size: 12px; opacity: 0.7; cursor: pointer; padding: 2px 6px; border-radius: 4px; background: rgba(255,255,255,0.2); transition: all 0.2s;" 
                      onmouseover="this.style.background='rgba(255,255,255,0.3)'" 
                      onmouseout="this.style.background='rgba(255,255,255,0.2)'"
                      onclick="event.stopPropagation(); window.通知管理器.remove(this.closest('.notification'))">×</span>
            </div>
        `;
        
        // 添加悬停效果
        this.addHoverEffects(notification);
        
        // 点击移除
        notification.addEventListener('click', () => {
            this.remove(notification);
        });
        
        return notification;
    }

    // 获取类型配置
    getTypeConfig(type) {
        const configs = {
            success: {
                background: 'linear-gradient(135deg, #4CAF50, #45a049)',
                icon: '✓'
            },
            error: {
                background: 'linear-gradient(135deg, #f44336, #da190b)',
                icon: '✕'
            },
            warning: {
                background: 'linear-gradient(135deg, #ff9800, #f57c00)',
                icon: '⚠'
            },
            info: {
                background: 'linear-gradient(135deg, #2196F3, #1976D2)',
                icon: 'ℹ'
            }
        };
        
        return configs[type] || configs.info;
    }

    // 添加悬停效果
    addHoverEffects(notification) {
        // 光泽效果
        const shimmer = document.createElement('div');
        shimmer.style.cssText = `
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
            pointer-events: none;
        `;
        notification.appendChild(shimmer);
        
        // 悬停事件
        notification.addEventListener('mouseenter', () => {
            notification.style.transform = 'translateX(-5px) scale(1.02)';
            notification.style.boxShadow = '0 6px 25px rgba(0, 0, 0, 0.2)';
            shimmer.style.left = '100%';
        });
        
        notification.addEventListener('mouseleave', () => {
            notification.style.transform = 'translateX(0) scale(1)';
            notification.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.15)';
            shimmer.style.left = '-100%';
        });
    }

    // 移除通知
    remove(notification) {
        if (!notification || !notification.parentNode) return;
        
        const index = this.notifications.indexOf(notification);
        if (index > -1) {
            this.notifications.splice(index, 1);
        }
        
        // 退出动画
        notification.style.transform = 'translateX(100%) scale(0.9)';
        notification.style.opacity = '0';
        notification.style.maxHeight = notification.offsetHeight + 'px';
        
        setTimeout(() => {
            notification.style.maxHeight = '0';
            notification.style.padding = '0 20px';
            notification.style.margin = '0';
        }, 200);
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 400);
    }

    // 清除所有通知
    clear() {
        [...this.notifications].forEach(notification => {
            this.remove(notification);
        });
    }

    // 显示成功通知
    success(message, duration) {
        return this.add(message, 'success', duration);
    }

    // 显示错误通知
    error(message, duration) {
        return this.add(message, 'error', duration);
    }

    // 显示警告通知
    warning(message, duration) {
        return this.add(message, 'warning', duration);
    }

    // 显示信息通知
    info(message, duration) {
        return this.add(message, 'info', duration);
    }
}

// 创建全局实例
window.通知管理器 = new 通知系统管理器();

// 兼容性函数
window.showNotification = function(message, type = 'info', duration = null) {
    return window.通知管理器.add(message, type, duration);
};

// 导出类（如果使用模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = 通知系统管理器;
}
