import logging
import requests
import json
import threading
import time
import traceback
import socket
from typing import Dict, Any, Optional
from datetime import datetime
from requests.exceptions import (
    ConnectionError, Timeout, RequestException,
    HTTPError, TooManyRedirects, SSLError
)

class 日志记录服务客户端:
    """日志记录服务2.0简化版客户端"""

    def __init__(self, url: str = 'http://222.186.21.133:8742/日志记录服务.php', timeout: int = 10):
        self.url = url
        self.timeout = timeout
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json; charset=utf-8',
            'User-Agent': 'SteamVault-Pro/1.0'
        })

    def send_log(self, level: str, message: str, extra: Dict[str, Any] = None,
                 user_code: str = None, encrypt: bool = False) -> bool:
        """
        发送日志到远程服务

        Args:
            level: 日志级别 (debug, info, warning, error, critical)
            message: 日志消息
            extra: 附加数据
            user_code: 用户激活码（如果用户已登录）
            encrypt: 是否加密传输

        Returns:
            bool: 发送是否成功
        """
        try:
            # 参数验证
            if not message:
                self._log_error("参数错误", "日志消息为空", {"level": level, "user_code": user_code})
                return False

            if level not in ['debug', 'info', 'warning', 'error', 'critical']:
                self._log_error("参数错误", f"无效的日志级别: {level}", {"message": message})
                return False

            # 构建日志消息，如果有用户激活码则添加前缀
            if user_code:
                formatted_message = f"[用户:{user_code[:12]}***] {message}"
            else:
                formatted_message = message

            data = {
                'level': level,
                'message': formatted_message,
                'extra': extra or {}
            }

            if encrypt:
                # 这里可以添加加密逻辑，暂时使用明文传输
                pass

            response = self.session.post(
                self.url,
                json=data,
                timeout=self.timeout
            )

            if response.status_code != 200:
                self._log_error("HTTP错误", f"服务器返回状态码: {response.status_code}", {
                    "response_text": response.text[:500],
                    "url": self.url
                })
                return False

            return True

        except ConnectionError as e:
            self._log_error("连接错误", "无法连接到日志服务器", {
                "error": str(e),
                "url": self.url,
                "error_type": "ConnectionError"
            })
            return False

        except Timeout as e:
            self._log_error("超时错误", f"请求超时({self.timeout}秒)", {
                "error": str(e),
                "url": self.url,
                "timeout": self.timeout,
                "error_type": "Timeout"
            })
            return False

        except SSLError as e:
            self._log_error("SSL错误", "SSL/TLS连接失败", {
                "error": str(e),
                "url": self.url,
                "error_type": "SSLError"
            })
            return False

        except TooManyRedirects as e:
            self._log_error("重定向错误", "重定向次数过多", {
                "error": str(e),
                "url": self.url,
                "error_type": "TooManyRedirects"
            })
            return False

        except HTTPError as e:
            self._log_error("HTTP错误", "HTTP协议错误", {
                "error": str(e),
                "url": self.url,
                "error_type": "HTTPError"
            })
            return False

        except json.JSONEncodeError as e:
            self._log_error("JSON编码错误", "数据序列化失败", {
                "error": str(e),
                "data": str(data)[:500],
                "error_type": "JSONEncodeError"
            })
            return False

        except UnicodeEncodeError as e:
            self._log_error("编码错误", "字符编码失败", {
                "error": str(e),
                "message": message[:100],
                "error_type": "UnicodeEncodeError"
            })
            return False

        except MemoryError as e:
            self._log_error("内存错误", "系统内存不足", {
                "error": str(e),
                "error_type": "MemoryError"
            })
            return False

        except OSError as e:
            self._log_error("系统错误", "操作系统级别错误", {
                "error": str(e),
                "errno": getattr(e, 'errno', None),
                "error_type": "OSError"
            })
            return False

        except RequestException as e:
            self._log_error("请求错误", "HTTP请求异常", {
                "error": str(e),
                "url": self.url,
                "error_type": "RequestException"
            })
            return False

        except Exception as e:
            self._log_error("未知错误", "发生未预期的错误", {
                "error": str(e),
                "error_type": type(e).__name__,
                "traceback": traceback.format_exc()
            })
            return False

    def _log_error(self, error_category: str, error_message: str, error_details: Dict[str, Any] = None) -> None:
        """
        内部错误日志记录方法，将错误信息发送到日志服务

        Args:
            error_category: 错误类别
            error_message: 错误消息
            error_details: 错误详细信息
        """
        try:
            # 构建错误日志数据
            error_data = {
                'level': 'error',
                'message': f"[日志服务客户端错误] {error_category}: {error_message}",
                'extra': {
                    'error_category': error_category,
                    'error_details': error_details or {},
                    'timestamp': datetime.now().isoformat(),
                    'client_version': '2.0'
                }
            }

            # 使用基础的requests直接发送，避免递归调用
            response = requests.post(
                self.url,
                json=error_data,
                timeout=5,  # 使用较短的超时时间
                headers={
                    'Content-Type': 'application/json; charset=utf-8',
                    'User-Agent': 'SteamVault-Pro/1.0'
                }
            )

        except Exception:
            # 如果连错误日志都发送失败，则完全静默，避免无限递归
            pass

    def log_async(self, level: str, message: str, extra: Dict[str, Any] = None,
                  user_code: str = None) -> None:
        """异步发送日志"""
        def _send():
            self.send_log(level, message, extra, user_code)

        thread = threading.Thread(target=_send, daemon=True)
        thread.start()

# 全局日志服务客户端实例
_log_service_client = 日志记录服务客户端()

def setup_logger():
    logger = logging.getLogger('游戏列表应用')
    logger.setLevel(logging.INFO)

    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    return logger

def send_remote_log(level: str, message: str, extra: Dict[str, Any] = None,
                   user_code: str = None, async_send: bool = True) -> None:
    """
    发送日志到远程服务的便捷函数

    Args:
        level: 日志级别 (debug, info, warning, error, critical)
        message: 日志消息
        extra: 附加数据
        user_code: 用户激活码（如果用户已登录）
        async_send: 是否异步发送
    """
    if async_send:
        _log_service_client.log_async(level, message, extra, user_code)
    else:
        _log_service_client.send_log(level, message, extra, user_code)

def get_current_user_code() -> Optional[str]:
    """获取当前登录用户的激活码"""
    try:
        from main import auth_manager
        auth_client = auth_manager.get_client()
        if auth_client and auth_client.is_authenticated():
            return auth_client.auth_code
    except:
        pass
    return None

logger = setup_logger()