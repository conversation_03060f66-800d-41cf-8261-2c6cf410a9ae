from .日志 import logger

def process_game_data(game_data):
    """处理游戏数据，包括格式转换和清洗"""
    processed_data = []
    
    try:
        logger.info("开始处理游戏数据")
        for item in game_data:
            parts = item.split("----")
            if len(parts) >= 4:
                game_name = parts[0].strip()
                game_type = format_game_type(parts[1].strip())
                game_id = parts[2].strip()
                time_info = parts[3].strip()
                
                processed_item = {
                    "名称": game_name,
                    "类型": game_type,
                    "ID": game_id,
                    "时间": time_info
                }
                processed_data.append(processed_item)
        
        logger.info(f"游戏数据处理完成，共处理{len(processed_data)}条记录")
        return processed_data
    except Exception as e:
        logger.error(f"数据处理过程中出错: {str(e)}")
        return []

def format_game_type(game_type):
    """将游戏类型格式化为首字母大写，其余小写"""
    if not game_type:
        return ""
    return game_type[0].upper() + game_type[1:].lower() 