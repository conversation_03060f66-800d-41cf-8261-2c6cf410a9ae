"""
网络验证系统核心模块
实现DES加密通信、机器码获取、网络验证客户端等功能
"""

import platform
import uuid
import hashlib
import time
import json
import base64
import requests
import threading
import logging
from typing import Dict, Any, Optional

# 延迟导入日志模块，避免循环导入
def _get_log_functions():
    try:
        from .日志 import send_remote_log
        return send_remote_log
    except ImportError:
        return None

_send_remote_log = None

try:
    from Crypto.Cipher import DES
    from Crypto.Util.Padding import pad, unpad
    CRYPTO_AVAILABLE = True
except ImportError:
    CRYPTO_AVAILABLE = False
    print("警告: pycryptodome未安装，网络验证功能将无法使用")
    print("请运行: pip install pycryptodome")


def 获取机器码() -> str:
    """
    获取设备唯一标识（机器码）
    基于MAC地址、CPU信息和系统信息生成
    """
    try:
        # 获取MAC地址
        mac = uuid.getnode()
        
        # 获取CPU信息
        cpu_info = platform.processor()
        
        # 获取系统信息
        system_info = f"{platform.system()}{platform.machine()}"
        
        # 组合生成机器码
        machine_string = f"{mac}|{cpu_info}|{system_info}"
        
        # 生成SHA256哈希并取前32位
        machine_code = hashlib.sha256(machine_string.encode('utf-8')).hexdigest()[:32]
        
        return machine_code.upper()
        
    except Exception as e:
        # 如果获取失败，使用备用方案
        fallback_string = f"{uuid.getnode()}|{platform.node()}|{platform.system()}"
        return hashlib.md5(fallback_string.encode('utf-8')).hexdigest()[:32].upper()


class DES加密工具:
    """
    DES加密解密工具类
    用于网络验证系统的数据加密通信
    """
    
    def __init__(self, key: str = 'NETAUTH2025'):
        if not CRYPTO_AVAILABLE:
            raise ImportError("pycryptodome未安装，无法使用加密功能")
            
        # 确保密钥长度为8字节
        self.key = (key + '00000000')[:8].encode('utf-8')
    
    def encrypt(self, plaintext: str) -> str:
        """加密文本"""
        try:
            cipher = DES.new(self.key, DES.MODE_ECB)
            
            if isinstance(plaintext, str):
                plaintext = plaintext.encode('utf-8')
                
            # 填充数据到8字节的倍数
            padded_data = pad(plaintext, DES.block_size)
            
            # 加密
            encrypted = cipher.encrypt(padded_data)
            
            # 返回Base64编码的结果
            return base64.b64encode(encrypted).decode('utf-8')
            
        except Exception as e:
            raise Exception(f"加密失败: {e}")
    
    def decrypt(self, ciphertext: str) -> str:
        """解密文本"""
        try:
            cipher = DES.new(self.key, DES.MODE_ECB)
            
            # Base64解码
            encrypted_data = base64.b64decode(ciphertext)
            
            # 解密
            decrypted = cipher.decrypt(encrypted_data)
            
            # 去除填充
            unpadded = unpad(decrypted, DES.block_size)
            
            return unpadded.decode('utf-8')
            
        except Exception as e:
            raise Exception(f"解密失败: {e}")
    
    def generate_signature(self, data: Dict[str, Any]) -> str:
        """生成数据签名"""
        try:
            # 按键名排序
            sorted_data = dict(sorted(data.items()))
            
            # 构建签名字符串
            sign_string = ''
            for key, value in sorted_data.items():
                if key != 'sign':
                    sign_string += f'{key}={value}&'
            
            # 添加密钥
            sign_string = sign_string.rstrip('&') + self.key.decode('utf-8')
            
            # 生成MD5签名
            return hashlib.md5(sign_string.encode('utf-8')).hexdigest()
            
        except Exception as e:
            raise Exception(f"签名生成失败: {e}")
    
    def create_secure_request(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """创建安全请求"""
        try:
            # 添加时间戳
            data['timestamp'] = int(time.time())
            
            # 生成签名
            data['sign'] = self.generate_signature(data)
            
            # 加密数据
            encrypted_data = self.encrypt(json.dumps(data, ensure_ascii=False))
            
            return {
                'encrypted_data': encrypted_data,
                'timestamp': int(time.time())
            }
            
        except Exception as e:
            raise Exception(f"创建安全请求失败: {e}")
    
    def parse_secure_response(self, encrypted_data: str) -> Dict[str, Any]:
        """解析安全响应"""
        try:
            # 解密数据
            decrypted_data = self.decrypt(encrypted_data)
            
            # 解析JSON
            return json.loads(decrypted_data)
            
        except Exception as e:
            raise Exception(f"解析安全响应失败: {e}")


class 网络验证客户端:
    """
    网络验证系统客户端
    实现登录验证、心跳检测、状态验证等功能
    """

    def _get_default_config(self) -> dict:
        """获取默认配置（不再依赖配置文件）"""
        return {
            "heartbeat_settings": {
                "interval_seconds": 600,  # 10分钟心跳间隔
                "timeout_seconds": 10,
                "max_consecutive_failures": 3,
                "enable_machine_code_verification": True,
                "critical_failure_keywords": ["封禁", "banned", "点数不足", "insufficient", "设备不匹配", "device", "无效", "invalid"],
                "immediate_exit_keywords": ["unauthorized", "401", "forbidden", "403"]
            },
            "network_settings": {
                "server_url": "http://222.186.21.133:8742",
                "api_endpoint": "/新版网络验证API.php",
                "request_timeout": 30
            },
            "logging_settings": {
                "enable_heartbeat_success_logs": True,
                "enable_detailed_error_logs": True,
                "log_points_info": True
            }
        }

    def __init__(self, server_url: str = "http://222.186.21.133:8742"):
        # 获取默认配置
        self.config = self._get_default_config()

        # 服务器配置
        network_settings = self.config.get('network_settings', {})
        self.server_url = network_settings.get('server_url', server_url)
        self.api_endpoint = network_settings.get('api_endpoint', "/新版网络验证API.php")
        self.request_timeout = network_settings.get('request_timeout', 30)

        # 初始化加密工具
        if not CRYPTO_AVAILABLE:
            raise ImportError("pycryptodome未安装，无法使用网络验证功能")
        self.encryption = DES加密工具()

        # 获取机器码
        self.machine_code = 获取机器码()

        # 验证状态
        self.auth_code = None
        self.session_token = None
        self.is_logged_in = False

        # 心跳检测配置
        heartbeat_settings = self.config.get('heartbeat_settings', {})
        self.heartbeat_thread = None
        self.heartbeat_interval = heartbeat_settings.get('interval_seconds', 600)
        self.heartbeat_timeout = heartbeat_settings.get('timeout_seconds', 10)
        self.max_consecutive_failures = heartbeat_settings.get('max_consecutive_failures', 3)
        self.enable_machine_code_verification = heartbeat_settings.get('enable_machine_code_verification', True)
        self.critical_failure_keywords = heartbeat_settings.get('critical_failure_keywords', [])
        self.immediate_exit_keywords = heartbeat_settings.get('immediate_exit_keywords', [])
        self.heartbeat_running = False
        self._last_heartbeat_error = ""  # 存储最后的心跳错误信息

        # 日志配置
        self.logging_settings = self.config.get('logging_settings', {})
        
        # 配置日志
        self.logger = logging.getLogger(__name__)
        # 设置日志级别为INFO，确保心跳信息能显示
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - 网络验证 - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def _send_request(self, data: Dict[str, Any], timeout: int = 30) -> Dict[str, Any]:
        """发送加密请求到服务器"""
        try:
            # 创建安全请求
            secure_request = self.encryption.create_secure_request(data)
            
            # 发送HTTP请求
            response = requests.post(
                f"{self.server_url}{self.api_endpoint}",
                json=secure_request,
                headers={
                    'Content-Type': 'application/json',
                    'User-Agent': 'SteamVault-Pro/1.0'
                },
                timeout=timeout
            )
            
            # 检查HTTP状态码
            if response.status_code != 200:
                raise Exception(f"HTTP错误: {response.status_code}")
            
            # 解析响应
            try:
                response_data = response.json()
            except ValueError as e:
                # JSON解析失败，可能是服务器返回了HTML错误页面
                response_text = response.text[:200] if response.text else "空响应"
                raise Exception(f"服务器响应格式错误，不是有效的JSON。响应内容: {response_text}")

            if 'encrypted_data' not in response_data:
                raise Exception(f"服务器响应格式错误，缺少encrypted_data字段。响应: {response_data}")

            # 解密响应数据
            return self.encryption.parse_secure_response(response_data['encrypted_data'])

        except requests.exceptions.Timeout:
            raise Exception("请求超时，请检查网络连接")
        except requests.exceptions.ConnectionError:
            raise Exception("无法连接到验证服务器，请检查网络连接和服务器地址")
        except Exception as e:
            if "服务器响应格式错误" in str(e):
                raise e  # 重新抛出我们自定义的错误
            raise Exception(f"网络请求失败: {e}")
    
    def login(self, auth_code: str) -> Dict[str, Any]:
        """用户登录验证"""
        global _send_remote_log
        if _send_remote_log is None:
            _send_remote_log = _get_log_functions()

        try:
            self.logger.info(f"开始登录验证")

            if _send_remote_log:
                _send_remote_log('info', '网络验证客户端开始登录', {
                    'auth_code_prefix': auth_code[:8] + '***' if len(auth_code) > 8 else auth_code,
                    'machine_code_prefix': self.machine_code[:16] + '...' if len(self.machine_code) > 16 else self.machine_code,
                    'function': 'network_auth_login'
                }, auth_code)

            # 构建登录请求数据
            login_data = {
                'action': 'login',
                'auth_code': auth_code,
                'machine_code': self.machine_code
            }

            # 发送登录请求
            response = self._send_request(login_data)

            # 检查登录结果
            if response.get('status') == 'success':
                self.auth_code = auth_code
                self.session_token = response.get('session_token')
                self.is_logged_in = True

                # 启动心跳检测
                self._start_heartbeat()

                self.logger.info("登录验证成功")

                if _send_remote_log:
                    _send_remote_log('info', '网络验证客户端登录成功', {
                        'session_token_available': bool(self.session_token),
                        'heartbeat_started': True,
                        'function': 'network_auth_login'
                    }, auth_code)

                return response
            else:
                error_msg = response.get('message', '登录失败')
                self.logger.error(f"登录验证失败")

                if _send_remote_log:
                    _send_remote_log('warning', '网络验证客户端登录失败', {
                        'error_message': error_msg,
                        'function': 'network_auth_login'
                    }, auth_code)

                raise Exception("登录验证失败")

        except Exception as e:
            self.logger.error(f"登录过程发生错误")

            if _send_remote_log:
                _send_remote_log('error', '网络验证客户端登录异常', {
                    'error': str(e),
                    'function': 'network_auth_login'
                }, auth_code if 'auth_code' in locals() else None)

            raise Exception("登录过程发生错误")
    
    def verify_status(self) -> bool:
        """验证当前登录状态"""
        if not self.is_logged_in or not self.auth_code or not self.session_token:
            self.logger.warning("🔍 状态验证失败")
            return False

        try:
            verify_data = {
                'action': 'verify',
                'auth_code': self.auth_code,
                'session_token': self.session_token
            }

            response = self._send_request(verify_data, timeout=10)

            if response.get('status') == 'valid':
                self.logger.info("状态验证通过")
                return True
            else:
                self.logger.warning("状态验证失败")
                return False

        except Exception as e:
            self.logger.warning("状态验证异常")
            return False

    def heartbeat(self) -> bool:
        """发送心跳检测"""
        if not self.is_logged_in or not self.auth_code:
            return False

        try:
            heartbeat_data = {
                'action': 'heartbeat',
                'auth_code': self.auth_code
            }

            # 根据配置决定是否添加机器码验证
            if self.enable_machine_code_verification:
                heartbeat_data['machine_code'] = self.machine_code

            response = self._send_request(heartbeat_data, timeout=self.heartbeat_timeout)

            # 检查心跳状态
            if response.get('status') == 'alive':
                # 记录剩余点数信息（模糊化）
                remaining_points = response.get('remaining_points')
                if remaining_points is not None:
                    self.logger.info(f"连接状态正常")
                return True
            else:
                # 处理各种失败情况
                error_message = response.get('message', '心跳检测失败')
                self._last_heartbeat_error = error_message  # 存储最后的错误信息
                self.logger.error(f"连接验证失败")

                # 根据错误类型进行特殊处理（模糊化日志）
                if any(keyword in error_message for keyword in ['封禁', 'banned']):
                    self.logger.critical(f"访问受限")
                elif '点数不足' in error_message or 'insufficient' in error_message.lower():
                    self.logger.critical(f"余额不足")
                elif '设备不匹配' in error_message or 'device' in error_message.lower():
                    self.logger.critical(f"设备验证失败")
                elif '无效' in error_message or 'invalid' in error_message.lower():
                    self.logger.critical(f"认证失效")

                return False

        except Exception as e:
            self.logger.warning(f"连接异常")
            return False

    def unbind_device(self) -> Dict[str, Any]:
        """设备解绑"""
        if not self.auth_code:
            raise Exception("请先登录")

        try:
            self.logger.info("开始设备解绑操作")

            unbind_data = {
                'action': 'unbind_device',
                'auth_code': self.auth_code
            }

            response = self._send_request(unbind_data)

            if response.get('status') == 'success':
                self.logger.info("设备解绑成功")
                # 解绑成功后清除登录状态
                self.logout()
                return response
            else:
                error_msg = response.get('message', '解绑失败')
                self.logger.error(f"设备解绑失败: {error_msg}")
                raise Exception(error_msg)

        except Exception as e:
            self.logger.error(f"解绑过程发生错误: {e}")
            raise

    def query_points(self) -> Dict[str, Any]:
        """查询激活码剩余点数"""
        if not self.auth_code:
            raise Exception("请先登录")

        try:
            self.logger.info("查询余额信息")

            query_data = {
                'action': 'query_points',
                'auth_code': self.auth_code
            }

            response = self._send_request(query_data)

            if response.get('status') == 'success':
                points_info = {
                    'remaining_points': response.get('remaining_points', 0),
                    'initial_points': response.get('initial_points', 0),
                    'total_used': response.get('total_used', 0)
                }
                self.logger.info(f"💰 余额查询成功")
                return points_info
            else:
                self.logger.error(f"余额查询失败")
                raise Exception("余额查询失败")

        except Exception as e:
            self.logger.error(f"查询余额失败")
            raise Exception("查询余额失败")

    def set_cloud_data(self, cloud_data: str) -> Dict[str, Any]:
        """设置云数据"""
        if not self.auth_code:
            raise Exception("请先登录")

        try:
            self.logger.info("保存配置数据")

            cloud_data_request = {
                'action': 'set_cloud_data',
                'auth_code': self.auth_code,
                'cloud_data': cloud_data
            }

            response = self._send_request(cloud_data_request)

            if response.get('status') == 'success':
                self.logger.info("配置保存成功")
                return response
            else:
                self.logger.error("配置保存失败")
                raise Exception("配置保存失败")

        except Exception as e:
            self.logger.error("保存配置失败")
            raise Exception("保存配置失败")

    def get_cloud_data(self) -> str:
        """获取云数据"""
        if not self.auth_code:
            raise Exception("请先登录")

        try:
            self.logger.info("加载配置数据")

            cloud_data_request = {
                'action': 'get_cloud_data',
                'auth_code': self.auth_code
            }

            response = self._send_request(cloud_data_request)

            if response.get('status') == 'success':
                cloud_data = response.get('cloud_data', '')
                self.logger.info("配置加载成功")
                return cloud_data
            else:
                self.logger.error("配置加载失败")
                raise Exception("配置加载失败")

        except Exception as e:
            self.logger.error("加载配置失败")
            raise Exception("加载配置失败")

    def query_unbind_info(self) -> Dict[str, Any]:
        """查询解绑信息"""
        if not self.auth_code:
            raise Exception("请先登录")

        try:
            query_data = {
                'action': 'query_unbind_info',
                'auth_code': self.auth_code
            }

            response = self._send_request(query_data)

            if response.get('status') == 'success':
                return response.get('unbind_info', {})
            else:
                error_msg = response.get('message', '查询失败')
                raise Exception(error_msg)

        except Exception as e:
            self.logger.error(f"查询解绑信息失败: {e}")
            raise

    def logout(self) -> None:
        """用户登出"""
        try:
            # 停止心跳检测
            self._stop_heartbeat()

            # 如果已登录，发送登出请求
            if self.is_logged_in and self.auth_code:
                try:
                    logout_data = {
                        'action': 'logout',
                        'auth_code': self.auth_code
                    }

                    self._send_request(logout_data, timeout=5)
                    self.logger.info("会话结束")

                except Exception as e:
                    # 登出时忽略网络错误
                    self.logger.warning("会话结束")

            # 清除登录状态
            self.auth_code = None
            self.session_token = None
            self.is_logged_in = False

            self.logger.info("用户已登出")

        except Exception as e:
            self.logger.error("登出过程发生错误")

    def _start_heartbeat(self) -> None:
        """启动心跳检测线程"""
        if self.heartbeat_thread and self.heartbeat_thread.is_alive():
            return

        self.heartbeat_running = True
        self.heartbeat_thread = threading.Thread(target=self._heartbeat_loop, daemon=True)
        self.heartbeat_thread.start()

        self.logger.info(f"连接监控已启动 (间隔: {self.heartbeat_interval}秒)")

    def _stop_heartbeat(self) -> None:
        """停止心跳检测线程"""
        self.heartbeat_running = False

        if self.heartbeat_thread and self.heartbeat_thread.is_alive():
            # 等待线程结束，最多等待2秒
            self.heartbeat_thread.join(timeout=2)

        self.logger.info("连接监控已停止")

    def _heartbeat_loop(self) -> None:
        """心跳检测循环"""
        consecutive_failures = 0
        max_failures = self.max_consecutive_failures
        critical_failure_types = self.critical_failure_keywords

        while self.heartbeat_running and self.is_logged_in:
            try:
                # 发送心跳检测
                heartbeat_result = self.heartbeat()

                if heartbeat_result:
                    consecutive_failures = 0  # 重置失败计数
                    # 心跳成功的日志已在heartbeat方法中记录
                else:
                    consecutive_failures += 1

                    # 检查是否为严重错误（需要立即退出）
                    last_error = getattr(self, '_last_heartbeat_error', '')
                    is_critical = any(keyword in last_error for keyword in critical_failure_types)

                    if is_critical:
                        self.logger.critical(f"检测到严重错误，程序退出")
                        self._force_exit("连接验证失败")
                        break

                    self.logger.warning(f"连接验证失败 ({consecutive_failures}/{max_failures})")

                    # 连续失败超过阈值，强制退出
                    if consecutive_failures >= max_failures:
                        self.logger.error("连接验证连续失败，程序将退出")
                        self._force_exit("连接验证连续失败")
                        break

            except Exception as e:
                consecutive_failures += 1
                self.logger.error(f"连接异常 ({consecutive_failures}/{max_failures})")

                # 网络异常也可能需要立即退出（如果是认证相关错误）
                error_str = str(e).lower()
                if any(keyword in error_str for keyword in self.immediate_exit_keywords):
                    self.logger.critical(f"认证异常，程序退出")
                    self._force_exit("认证异常")
                    break

                # 连续失败超过阈值，强制退出
                if consecutive_failures >= max_failures:
                    self.logger.error("连接异常持续，程序将退出")
                    self._force_exit("连接异常持续")
                    break

            # 等待下次心跳
            for _ in range(self.heartbeat_interval):
                if not self.heartbeat_running:
                    break
                time.sleep(1)

    def _force_exit(self, reason: str = "验证失败") -> None:
        """强制退出程序"""
        self.logger.critical(f"程序即将退出")

        # 记录退出原因和时间（内部记录，不输出敏感信息）
        import datetime
        exit_info = {
            'reason': "连接验证失败",  # 模糊化原因
            'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'session': "已结束"  # 不显示具体的激活码信息
        }

        self.logger.critical(f"会话结束")

        # 清理状态
        self.is_logged_in = False
        self.heartbeat_running = False

        # 尝试发送登出请求（快速超时）
        try:
            if self.auth_code:
                logout_data = {
                    'action': 'logout',
                    'auth_code': self.auth_code
                }
                self._send_request(logout_data, timeout=2)
        except:
            pass  # 忽略登出错误

        # 强制退出
        import sys
        import os

        try:
            # 尝试优雅退出
            sys.exit(1)
        except:
            # 强制终止进程
            os._exit(1)

    def get_machine_code(self) -> str:
        """获取当前机器码"""
        return self.machine_code

    def is_authenticated(self) -> bool:
        """检查是否已认证"""
        return self.is_logged_in and bool(self.auth_code) and bool(self.session_token)
