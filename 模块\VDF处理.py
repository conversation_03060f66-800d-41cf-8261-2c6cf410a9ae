import os
import re
import requests
from .日志 import logger, send_remote_log, get_current_user_code


def 获取DLC信息(app_id):
    """
    从API获取游戏的DLC信息

    Args:
        app_id (str): 游戏的AppID

    Returns:
        dict: DLC信息结果
    """
    user_code = get_current_user_code()
    send_remote_log('info', '开始获取游戏DLC信息', {
        'app_id': app_id,
        'function': '获取DLC信息'
    }, user_code)

    try:
        # 发送POST请求获取DLC信息
        response = requests.post(
            "http://steamlab.cc/GetDlc.php",
            data={"appid": app_id},
            timeout=10
        )

        if response.status_code == 200:
            result = response.json()

            # 检查响应消息
            if result.get("message") == "数据获取成功":
                dlc_list = result.get("data", {}).get("dlc", [])
                send_remote_log('info', '成功获取游戏DLC信息', {
                    'app_id': app_id,
                    'dlc_count': len(dlc_list),
                    'has_dlc': len(dlc_list) > 0
                }, user_code)
                return {
                    "status": "success",
                    "dlc_count": len(dlc_list),
                    "dlc_list": dlc_list,
                    "message": f"成功获取 {len(dlc_list)} 个DLC"
                }
            elif result.get("message") == "Steam API返回失败状态":
                send_remote_log('warning', '获取DLC信息失败：Steam API返回失败', {
                    'app_id': app_id,
                    'api_response': result.get("message")
                }, user_code)
                return {
                    "status": "failed",
                    "dlc_count": 0,
                    "dlc_list": [],
                    "message": "Steam API返回失败状态"
                }
            else:
                send_remote_log('warning', '获取DLC信息响应异常', {
                    'app_id': app_id,
                    'response_message': result.get('message', '无消息')
                }, user_code)
                return {
                    "status": "error",
                    "dlc_count": 0,
                    "dlc_list": [],
                    "message": f"未知响应: {result.get('message', '无消息')}"
                }
        else:
            return {
                "status": "error",
                "dlc_count": 0,
                "dlc_list": [],
                "message": f"HTTP请求失败: {response.status_code}"
            }

    except requests.exceptions.Timeout:
        return {
            "status": "error",
            "dlc_count": 0,
            "dlc_list": [],
            "message": "请求超时"
        }
    except Exception as e:
        return {
            "status": "error",
            "dlc_count": 0,
            "dlc_list": [],
            "message": f"获取DLC信息失败: {str(e)}"
        }

class VDF解析器:
    """VDF文件解析器类"""
    
    def __init__(self):
        self.depot_data = {}
    
    def 解析VDF文件(self, 文件路径):
        """
        解析VDF文件，提取depot信息和DecryptionKey

        Args:
            文件路径 (str): VDF文件的完整路径

        Returns:
            dict: 包含解析结果的字典
        """
        try:
            if not os.path.exists(文件路径):
                return {"status": "error", "message": "VDF文件不存在"}

            with open(文件路径, 'r', encoding='utf-8') as f:
                内容 = f.read()

            # 解析VDF内容
            解析结果 = self._解析VDF内容(内容)
            return 解析结果

        except Exception as e:
            return {"status": "error", "message": f"解析VDF文件异常: {str(e)}"}
    
    def _解析VDF内容(self, 内容):
        """
        解析VDF文件内容 - 使用逐行解析方法

        Args:
            内容 (str): VDF文件的文本内容

        Returns:
            dict: 解析结果
        """
        try:
            depots = {}

            # 逐行解析VDF内容
            lines = 内容.strip().split('\n')
            in_depots = False
            in_depot = False
            current_depot_id = None
            depot_level = 0

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 查找depots块开始
                if '"depots"' in line:
                    in_depots = True
                    continue

                if in_depots:
                    if line == '{':
                        depot_level += 1
                    elif line == '}':
                        depot_level -= 1
                        if depot_level == 0:
                            in_depots = False
                        elif depot_level == 1 and in_depot:
                            in_depot = False
                            current_depot_id = None
                    elif depot_level == 1 and line.startswith('"') and line.endswith('"'):
                        # 这是一个depot ID
                        current_depot_id = line.strip('"')
                        in_depot = True
                        depots[current_depot_id] = {"DecryptionKey": None}
                    elif in_depot and 'DecryptionKey' in line:
                        # 解析DecryptionKey
                        key_pattern = r'"DecryptionKey"\s*"([^"]+)"'
                        key_match = re.search(key_pattern, line)
                        if key_match:
                            decryption_key = key_match.group(1)
                            depots[current_depot_id]['DecryptionKey'] = decryption_key

            if not depots:
                return {"status": "error", "message": "未找到任何depot信息"}

            return {
                "status": "success",
                "depots": depots
            }

        except Exception as e:
            return {"status": "error", "message": f"解析VDF内容异常: {str(e)}"}
    
    def 扫描目录中的VDF文件(self, 目录路径):
        """
        扫描指定目录中的所有VDF文件

        Args:
            目录路径 (str): 要扫描的目录路径

        Returns:
            list: VDF文件路径列表
        """
        try:
            vdf_files = []

            if not os.path.exists(目录路径):
                return vdf_files

            for root, dirs, files in os.walk(目录路径):
                for file in files:
                    if file.lower().endswith('.vdf'):
                        vdf_path = os.path.join(root, file)
                        vdf_files.append(vdf_path)

            return vdf_files

        except Exception as e:
            return []

    def 扫描目录中的Lua文件(self, 目录路径, app_id):
        """
        扫描指定目录中的Lua文件

        Args:
            目录路径 (str): 要扫描的目录路径
            app_id (str): 应用ID

        Returns:
            list: Lua文件路径列表
        """
        try:
            lua_files = []

            if not os.path.exists(目录路径):
                return lua_files

            for root, dirs, files in os.walk(目录路径):
                for file in files:
                    if file.lower().endswith('.lua') and (file.startswith(app_id) or app_id in file):
                        lua_path = os.path.join(root, file)
                        lua_files.append(lua_path)

            return lua_files

        except Exception as e:
            return []

class Lua文件生成器:
    """Lua文件生成器类"""
    
    def __init__(self):
        pass
    
    def 生成Lua文件(self, depots_data, app_id, 输出路径, dlc_list=None):
        """
        根据depot数据生成Lua文件，支持DLC

        Args:
            depots_data (dict): depot数据字典
            app_id (str): 主应用ID
            输出路径 (str): Lua文件输出路径
            dlc_list (list): DLC AppID列表

        Returns:
            dict: 生成结果
        """
        try:
            lua_lines = []

            # 首先添加主应用ID（不带DecryptionKey）
            lua_lines.append(f"addappid({app_id})")

            # 按depot ID排序
            sorted_depots = sorted(depots_data.items(), key=lambda x: int(x[0]))

            for depot_id, depot_info in sorted_depots:
                decryption_key = depot_info.get("DecryptionKey")

                if decryption_key:
                    # 有DecryptionKey的depot
                    lua_lines.append(f'addappid({depot_id},0,"{decryption_key}")')
                else:
                    # 没有DecryptionKey的depot
                    lua_lines.append(f"addappid({depot_id})")

            # 添加DLC AppID（如果存在）
            dlc_count = 0
            if dlc_list:
                for dlc_id in dlc_list:
                    lua_lines.append(f"addappid({dlc_id})")
                    dlc_count += 1

            # 写入文件
            with open(输出路径, 'w', encoding='utf-8') as f:
                for line in lua_lines:
                    f.write(line + '\n')

            return {
                "status": "success",
                "文件路径": 输出路径,
                "行数": len(lua_lines),
                "内容": lua_lines,
                "dlc_count": dlc_count
            }

        except Exception as e:
            from .日志 import send_remote_log, get_current_user_code
            user_code = get_current_user_code()
            send_remote_log('error', 'Lua文件生成异常', {
                'app_id': app_id,
                'error': str(e),
                'failure_stage': 'Lua文件生成',
                'error_category': 'VDF错误'
            }, user_code)
            return {"status": "error", "message": f"生成文件异常: {str(e)}"}

    def 更新Lua文件追加DLC(self, lua_文件路径, dlc_list):
        """
        更新已存在的Lua文件，追加DLC信息

        Args:
            lua_文件路径 (str): 已存在的Lua文件路径
            dlc_list (list): DLC AppID列表

        Returns:
            dict: 更新结果
        """
        try:
            print(f"🔧 开始处理DLC追加: {lua_文件路径}")
            print(f"📋 待处理DLC列表: {dlc_list}")

            if not dlc_list:
                print("❌ 无DLC需要追加")
                return {
                    "status": "success",
                    "message": "无DLC需要追加",
                    "added_count": 0
                }

            # 读取现有文件内容
            with open(lua_文件路径, 'r', encoding='utf-8') as f:
                existing_lines = f.readlines()

            print(f"📖 读取到 {len(existing_lines)} 行现有内容")

            # 提取现有的AppID，避免重复添加
            existing_appids = set()
            for line in existing_lines:
                line = line.strip()
                if line.startswith('addappid('):
                    # 提取AppID
                    import re
                    match = re.search(r'addappid\((\d+)', line)
                    if match:
                        existing_appids.add(match.group(1))

            print(f"🔍 现有AppID列表: {sorted(existing_appids)}")

            # 过滤出需要新增的DLC
            new_dlc_list = []
            for dlc_id in dlc_list:
                if str(dlc_id) not in existing_appids:
                    new_dlc_list.append(dlc_id)

            print(f"🆕 需要新增的DLC: {new_dlc_list}")
            print(f"🔄 重复的DLC (将跳过): {[dlc for dlc in dlc_list if str(dlc) in existing_appids]}")

            if not new_dlc_list:
                print("✅ 所有DLC已存在，无需追加")
                return {
                    "status": "success",
                    "message": "所有DLC已存在，无需追加",
                    "added_count": 0
                }

            # 准备要追加的DLC行
            dlc_lines = []
            for dlc_id in new_dlc_list:
                dlc_lines.append(f"addappid({dlc_id})\n")

            print(f"📝 准备追加的DLC行:")
            for line in dlc_lines:
                print(f"   {line.strip()}")

            # 显示预期的最终内容
            print(f"🎯 预期的最终Lua文件内容:")
            print("=" * 30)
            for line in existing_lines:
                print(line.rstrip())
            for line in dlc_lines:
                print(line.rstrip())
            print("=" * 30)

            # 确保原有内容的最后一行有换行符
            if existing_lines and not existing_lines[-1].endswith('\n'):
                existing_lines[-1] += '\n'
                print(f"🔧 修正最后一行，添加换行符")

            # 写入更新后的内容
            with open(lua_文件路径, 'w', encoding='utf-8') as f:
                # 写入原有内容
                f.writelines(existing_lines)
                # 追加DLC内容
                f.writelines(dlc_lines)

            print(f"✅ 成功写入文件，追加了 {len(new_dlc_list)} 个DLC")

            return {
                "status": "success",
                "message": f"成功追加 {len(new_dlc_list)} 个DLC",
                "added_count": len(new_dlc_list),
                "added_dlc": new_dlc_list
            }

        except Exception as e:
            from .日志 import send_remote_log, get_current_user_code
            user_code = get_current_user_code()
            send_remote_log('error', 'Lua文件DLC追加异常', {
                'lua_file_path': lua_文件路径,
                'error': str(e),
                'failure_stage': 'Lua文件DLC追加',
                'error_category': 'VDF错误'
            }, user_code)
            return {"status": "error", "message": f"追加DLC异常: {str(e)}"}

def 处理解压目录中的VDF文件(解压目录路径, app_id):
    """
    处理解压目录中的VDF文件，生成对应的Lua文件
    优先级：已存在的Lua文件 > 生成新的Lua文件

    Args:
        解压目录路径 (str): 解压后的目录路径
        app_id (str): 应用ID

    Returns:
        dict: 处理结果
    """
    user_code = get_current_user_code()
    send_remote_log('info', '开始处理文件', {
        'app_id': app_id,
        'extract_path': 解压目录路径,
        'function': '处理解压目录中的文件'
    }, user_code)

    try:
        # 创建VDF解析器和Lua生成器
        vdf_parser = VDF解析器()
        lua_generator = Lua文件生成器()

        # 首先检查是否已存在Lua文件
        existing_lua_files = vdf_parser.扫描目录中的Lua文件(解压目录路径, app_id)

        if existing_lua_files:
            # 已存在Lua文件，检查并追加DLC信息
            existing_lua_path = existing_lua_files[0]

            # 调试输出：显示检测到的已存在文件
            logger.info(f"🔍 检测到已存在的Lua文件: {existing_lua_path}")
            print(f"🔍 检测到已存在的Lua文件: {existing_lua_path}")

            # 读取并显示当前文件内容
            try:
                with open(existing_lua_path, 'r', encoding='utf-8') as f:
                    current_content = f.read()
                logger.info(f"📄 当前Lua文件内容:\n{current_content}")
                print(f"📄 当前Lua文件内容:\n{current_content}")
                print("=" * 50)
            except Exception as e:
                logger.warning(f"⚠️ 无法读取当前Lua文件内容: {e}")
                print(f"⚠️ 无法读取当前Lua文件内容: {e}")

            # 获取DLC信息
            logger.info("🔍 开始检查DLC信息...")
            print("🔍 开始检查DLC信息...")
            dlc_结果 = 获取DLC信息(app_id)
            dlc_list = dlc_结果.get("dlc_list", []) if dlc_结果["status"] == "success" else []
            dlc_count = len(dlc_list)

            # 调试输出：显示DLC获取结果
            logger.info(f"🎮 DLC获取结果: {dlc_结果}")
            print(f"🎮 DLC获取结果: {dlc_结果}")
            if dlc_list:
                logger.info(f"📋 找到的DLC列表: {dlc_list}")
                print(f"📋 找到的DLC列表: {dlc_list}")
            else:
                logger.info("❌ 未找到DLC或获取失败")
                print("❌ 未找到DLC或获取失败")

            if dlc_count > 0:
                # 有DLC需要追加
                logger.info(f"✅ 找到 {dlc_count} 个DLC，开始更新已存在的Lua文件...")
                print(f"✅ 找到 {dlc_count} 个DLC，开始更新已存在的Lua文件...")
                更新结果 = lua_generator.更新Lua文件追加DLC(existing_lua_path, dlc_list)

                if 更新结果["status"] == "success":
                    # 显示最终更新后的文件内容
                    try:
                        with open(existing_lua_path, 'r', encoding='utf-8') as f:
                            final_content = f.read()
                        logger.info(f"🎉 最终更新后的Lua文件内容:\n{final_content}")
                        print(f"🎉 最终更新后的Lua文件内容:\n{final_content}")
                        print("=" * 50)
                    except Exception as e:
                        logger.warning(f"⚠️ 无法读取最终文件内容: {e}")
                        print(f"⚠️ 无法读取最终文件内容: {e}")

                    send_remote_log('info', '成功更新已存在的文件，追加DLC信息', {
                        'app_id': app_id,
                        'lua_file_path': existing_lua_path,
                        'dlc_count': dlc_count,
                        'added_dlc_count': 更新结果.get('added_count', 0),
                        'action': 'updated_existing_with_dlc'
                    }, user_code)

                    print(f"✅ DLC追加完成! 实际追加了 {更新结果.get('added_count', 0)} 个新DLC")

                    return {
                        "status": "success",
                        "message": f"发现已存在的文件，已追加 {更新结果.get('added_count', 0)} 个新DLC",
                        "lua_文件路径": existing_lua_path,
                        "dlc_数量": dlc_count,
                        "实际追加数量": 更新结果.get('added_count', 0),
                        "dlc_结果": dlc_结果,
                        "更新详情": 更新结果,
                        "action": "updated_existing_with_dlc"
                    }
                else:
                    send_remote_log('warning', '更新已存在文件失败，保持原文件不变', {
                        'app_id': app_id,
                        'lua_file_path': existing_lua_path,
                        'error': 更新结果.get('message', '未知错误')
                    }, user_code)
            else:
                # 没有DLC或获取失败，保持原文件不变
                send_remote_log('info', '发现已存在的文件，无DLC需要追加', {
                    'app_id': app_id,
                    'lua_file_path': existing_lua_path,
                    'action': 'skipped_existing_no_dlc'
                }, user_code)

            return {
                "status": "success",
                "message": f"发现已存在的文件，保持不变",
                "lua_文件路径": existing_lua_path,
                "dlc_数量": dlc_count,
                "dlc_结果": dlc_结果,
                "action": "skipped_existing"
            }

        # 扫描VDF文件
        vdf_files = vdf_parser.扫描目录中的VDF文件(解压目录路径)

        if not vdf_files:
            # 没有VDF文件也没有Lua文件，返回错误
            send_remote_log('error', '处理失败：未找到VDF文件', {
                'app_id': app_id,
                'extract_path': 解压目录路径,
                'failure_stage': 'VDF文件扫描',
                'error_category': 'VDF错误'
            }, user_code)
            return {"status": "error", "message": "未找到文件"}

        send_remote_log('info', '开始解析文件', {
            'app_id': app_id,
            'vdf_files_count': len(vdf_files),
            'vdf_files': [os.path.basename(f) for f in vdf_files]
        }, user_code)

        # 合并所有VDF文件的depot数据
        all_depots = {}
        vdf_目录 = None

        for vdf_file in vdf_files:
            解析结果 = vdf_parser.解析VDF文件(vdf_file)

            if 解析结果["status"] == "success":
                all_depots.update(解析结果["depots"])
                # 记录VDF文件所在目录，用于放置Lua文件
                if vdf_目录 is None:
                    vdf_目录 = os.path.dirname(vdf_file)

        if not all_depots:
            send_remote_log('warning', '处理失败：所有文件解析失败', {
                'app_id': app_id,
                'vdf_files_count': len(vdf_files)
            }, user_code)
            return {"status": "error", "message": "所有文件解析失败"}

        # 生成Lua文件到VDF文件相同目录
        if vdf_目录 is None:
            vdf_目录 = 解压目录路径  # 备用方案

        # 检测DLC信息
        logger.info("Checking DLC information...")
        dlc_结果 = 获取DLC信息(app_id)
        dlc_list = dlc_结果.get("dlc_list", []) if dlc_结果["status"] == "success" else []
        dlc_count = len(dlc_list)

        if dlc_count > 0:
            logger.info(f"Found {dlc_count} DLC items")

        lua_文件路径 = os.path.join(vdf_目录, f"{app_id}.lua")
        生成结果 = lua_generator.生成Lua文件(all_depots, app_id, lua_文件路径, dlc_list)

        if 生成结果["status"] == "success":
            send_remote_log('info', '处理完成', {
                'app_id': app_id,
                'vdf_files_count': len(vdf_files),
                'depot_count': len(all_depots),
                'dlc_count': dlc_count,
                'lua_file_path': lua_文件路径,
                'action': 'generated_new'
            }, user_code)
            return {
                "status": "success",
                "message": f"成功处理",
                "vdf_文件数量": len(vdf_files),
                "depot_数量": len(all_depots),
                "dlc_数量": dlc_count,
                "dlc_结果": dlc_结果,
                "lua_文件路径": lua_文件路径,
                "lua_内容": 生成结果["内容"],
                "action": "generated_new"
            }
        else:
            send_remote_log('error', '处理失败', {
                'app_id': app_id,
                'error': 生成结果.get('message', '未知错误')
            }, user_code)
            return 生成结果

    except Exception as e:
        import traceback
        send_remote_log('error', 'VDF处理异常', {
            'app_id': app_id,
            'error': str(e),
            'extract_path': 解压目录路径,
            'failure_stage': 'VDF处理',
            'error_category': 'VDF错误',
            'traceback': traceback.format_exc()
        }, user_code)
        return {"status": "error", "message": f"处理文件异常: {str(e)}"}
