/* 特效相关样式 */

/* 鼠标点击特效相关样式 */
body.is-pressed {
    cursor: none;
}

body.is-longpress {
    cursor: crosshair;
}

/* 鼠标指针样式 */
.pointer {
    position: fixed;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0.2) 70%, transparent 100%);
    pointer-events: none;
    z-index: 99998;
    transform: translate(-50%, -50%);
    transition: all 0.1s ease;
    display: none;
}

body.is-pressed .pointer {
    display: block;
    background: radial-gradient(circle, rgba(255,100,100,0.8) 0%, rgba(255,100,100,0.2) 70%, transparent 100%);
    transform: translate(-50%, -50%) scale(1.2);
}

body.is-longpress .pointer {
    background: radial-gradient(circle, rgba(255,50,50,0.9) 0%, rgba(255,50,50,0.3) 70%, transparent 100%);
    transform: translate(-50%, -50%) scale(1.5);
    animation: pulse-pointer 0.5s infinite alternate;
}

@keyframes pulse-pointer {
    from {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0.8;
    }
    to {
        transform: translate(-50%, -50%) scale(1.8);
        opacity: 1;
    }
}

/* 特效画布通用样式 */
.effect-canvas {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* 性能优化相关样式 */
@media (max-width: 768px) {
    /* 在小屏幕设备上减少特效复杂度 */
    .effect-canvas {
        opacity: 0.7;
    }
}

@media (prefers-reduced-motion: reduce) {
    /* 尊重用户的减少动画偏好 */
    .effect-canvas {
        display: none !important;
    }
    
    body.is-pressed,
    body.is-longpress {
        cursor: default;
    }
    
    .pointer {
        display: none !important;
    }
}

/* 低性能设备优化 */
.performance-mode .effect-canvas {
    opacity: 0.5;
    filter: blur(0.5px);
}

.performance-mode body.is-pressed,
.performance-mode body.is-longpress {
    cursor: default;
}

.performance-mode .pointer {
    display: none;
}

/* 特效加载状态 */
.effects-loading {
    position: relative;
}

.effects-loading::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.01);
    z-index: -1;
    pointer-events: none;
}

/* 特效错误状态 */
.effects-error {
    /* 静默处理特效错误，不显示任何错误信息 */
}

/* 响应式设计适配 */
@media (max-width: 480px) {
    .pointer {
        width: 15px;
        height: 15px;
    }
    
    body.is-pressed .pointer {
        transform: translate(-50%, -50%) scale(1.1);
    }
    
    body.is-longpress .pointer {
        transform: translate(-50%, -50%) scale(1.3);
    }
}

/* 高DPI屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .effect-canvas {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
    .pointer {
        background: radial-gradient(circle, rgba(200,200,255,0.8) 0%, rgba(200,200,255,0.2) 70%, transparent 100%);
    }
    
    body.is-pressed .pointer {
        background: radial-gradient(circle, rgba(255,150,150,0.8) 0%, rgba(255,150,150,0.2) 70%, transparent 100%);
    }
    
    body.is-longpress .pointer {
        background: radial-gradient(circle, rgba(255,100,100,0.9) 0%, rgba(255,100,100,0.3) 70%, transparent 100%);
    }
}

/* 特效容器 */
.effects-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1000;
    overflow: hidden;
}

/* 特效性能监控 */
.effects-performance-monitor {
    position: fixed;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 12px;
    font-family: monospace;
    z-index: 99999;
    display: none;
    pointer-events: none;
}

/* 调试模式下显示性能监控 */
.debug-mode .effects-performance-monitor {
    display: block;
}

/* 特效淡入淡出动画 */
.effect-fade-in {
    animation: effectFadeIn 0.5s ease-in-out;
}

.effect-fade-out {
    animation: effectFadeOut 0.5s ease-in-out;
}

@keyframes effectFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes effectFadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

/* 特效禁用状态 */
.effects-disabled .effect-canvas {
    display: none !important;
}

.effects-disabled .pointer {
    display: none !important;
}

.effects-disabled body.is-pressed,
.effects-disabled body.is-longpress {
    cursor: default !important;
}
