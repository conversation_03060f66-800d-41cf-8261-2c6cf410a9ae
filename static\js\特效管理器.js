// 特效管理器 - 统一管理页面特效
(function() {
    'use strict';
    
    // 检查是否已经初始化过
    if (window.effectManagerInitialized) {
        return;
    }
    window.effectManagerInitialized = true;
    
    // 特效管理器
    const EffectManager = {
        // 配置选项
        config: {
            enableClickEffect: true,
            performanceMode: false, // 性能模式，在低端设备上禁用特效
            maxEffects: 1 // 最大同时运行的特效数量
        },
        
        // 已加载的特效
        loadedEffects: new Set(),
        
        // 性能检测
        detectPerformance: function() {
            // 简单的性能检测
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            
            // 检测设备性能指标
            const isLowEnd = (
                navigator.hardwareConcurrency < 4 || // CPU核心数少于4
                (gl && gl.getParameter && gl.getParameter(gl.MAX_TEXTURE_SIZE) < 4096) || // GPU性能较低
                window.innerWidth < 768 // 小屏幕设备
            );

            if (isLowEnd) {
                this.config.performanceMode = true;
            }
        },
        
        // 加载特效脚本
        loadEffect: function(effectName, scriptPath) {
            return new Promise((resolve, reject) => {
                if (this.loadedEffects.has(effectName)) {
                    resolve();
                    return;
                }

                const script = document.createElement('script');
                script.src = scriptPath;

                // 为外部CDN设置更长的超时时间
                const timeout = setTimeout(() => {
                    reject(new Error(`Timeout loading effect: ${effectName}`));
                }, 10000); // 10秒超时

                script.onload = () => {
                    clearTimeout(timeout);
                    this.loadedEffects.add(effectName);
                    resolve();
                };

                script.onerror = () => {
                    clearTimeout(timeout);
                    reject(new Error(`Failed to load effect: ${effectName}`));
                };

                document.head.appendChild(script);
            });
        },
        
        // 初始化特效
        initEffects: function() {
            // 性能检测
            this.detectPerformance();

            const effectPromises = [];

            // 加载鼠标点击特效
            if (this.config.enableClickEffect) {
                effectPromises.push(
                    this.loadEffect('clickEffect', '/static/js/鼠标点击特效.js')
                        .catch(err => console.warn('鼠标点击特效加载失败:', err))
                );
            }

            // 等待所有特效加载完成
            Promise.allSettled(effectPromises).then(results => {
                const successCount = results.filter(r => r.status === 'fulfilled').length;
                console.log(`特效管理器: 成功加载 ${successCount} 个特效`);
            });
        },
        
        // 页面可见性变化处理
        handleVisibilityChange: function() {
            if (document.hidden) {
                // 页面隐藏时暂停特效以节省性能
                this.pauseEffects();
            } else {
                // 页面显示时恢复特效
                this.resumeEffects();
            }
        },
        
        // 暂停特效
        pauseEffects: function() {
            // 发送自定义事件通知特效暂停
            window.dispatchEvent(new CustomEvent('effectsPause'));
        },
        
        // 恢复特效
        resumeEffects: function() {
            // 发送自定义事件通知特效恢复
            window.dispatchEvent(new CustomEvent('effectsResume'));
        },
        
        // 清理所有特效
        cleanup: function() {
            // 发送清理事件
            window.dispatchEvent(new CustomEvent('effectsCleanup'));
            this.loadedEffects.clear();
            window.effectManagerInitialized = false;
        }
    };
    
    // 页面可见性变化监听
    document.addEventListener('visibilitychange', function() {
        EffectManager.handleVisibilityChange();
    });
    
    // 页面卸载时清理
    window.addEventListener('beforeunload', function() {
        EffectManager.cleanup();
    });
    
    // 响应式处理 - 窗口大小变化时重新检测性能
    let resizeTimer;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(function() {
            EffectManager.detectPerformance();
        }, 250);
    });
    
    // 初始化函数
    function init() {
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                setTimeout(() => EffectManager.initEffects(), 100);
            });
        } else {
            setTimeout(() => EffectManager.initEffects(), 100);
        }
    }
    
    // 暴露到全局作用域供调试使用
    window.EffectManager = EffectManager;
    
    // 开始初始化
    init();
    
})();
