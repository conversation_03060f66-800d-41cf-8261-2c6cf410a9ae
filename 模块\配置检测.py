import os
import sys
import zipfile
import shutil
import winreg
import subprocess
from .日志 import logger, send_remote_log, get_current_user_code
from .路径工具 import 获取应用程序路径, 获取配置文件路径

def _check_steam_installation():
    """检测Steam是否安装（内部函数，避免循环导入）"""
    try:
        # 方法1: 检查注册表
        try:
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\Valve\Steam") as key:
                install_path = winreg.QueryValueEx(key, "InstallPath")[0]
                steam_exe = os.path.join(install_path, "steam.exe")
                if os.path.exists(steam_exe):
                    return {"installed": True, "path": install_path, "method": "registry"}
        except (FileNotFoundError, OSError):
            pass

        # 方法2: 检查常见安装路径
        common_paths = [
            r"C:\Program Files (x86)\Steam\steam.exe",
            r"C:\Program Files\Steam\steam.exe",
            r"D:\Steam\steam.exe",
            r"E:\Steam\steam.exe",
            r"F:\Steam\steam.exe"
        ]

        for path in common_paths:
            if os.path.exists(path):
                return {"installed": True, "path": os.path.dirname(path), "method": "common_path"}

        # 方法3: 检查PATH环境变量
        steam_path = shutil.which("steam")
        if steam_path:
            return {"installed": True, "path": os.path.dirname(steam_path), "method": "path"}

        return {"installed": False, "path": None, "method": None}

    except Exception as e:
        return {"installed": False, "path": None, "method": None, "error": str(e)}

# 将路径获取函数移动到单独的模块中，避免重复代码

class Steam配置检测器:
    """Steam配置文件检测器"""

    def __init__(self):
        self.required_items = [
            "hid.dll",
            "config",
            "depotcache",
            "config/stplug-in",
            "config/depotcache"
        ]
        self.steam_path = None
        # 使用路径工具获取配置文件路径
        self.config_zip_path = 获取配置文件路径()
    
    def 获取Steam路径(self):
        """获取Steam安装路径"""
        try:
            steam_info = _check_steam_installation()
            if steam_info["installed"] and steam_info["path"]:
                self.steam_path = steam_info["path"]
                return True
            return False
        except:
            return False
    
    def 检测配置文件(self):
        """
        检测Steam配置文件是否完整
        
        Returns:
            dict: 检测结果
        """
        try:
            # 获取Steam路径
            if not self.获取Steam路径():
                return {
                    "status": "error",
                    "message": "Steam not detected",
                    "missing_items": [],
                    "auto_fixed": False
                }
            
            # 检测必需的文件和文件夹
            missing_items = []
            
            for item in self.required_items:
                item_path = os.path.join(self.steam_path, item)
                if not os.path.exists(item_path):
                    missing_items.append(item)
            
            if not missing_items:
                return {
                    "status": "success", 
                    "message": "Configuration complete",
                    "missing_items": [],
                    "auto_fixed": False
                }
            
            # 尝试自动修复
            auto_fix_result = self._自动修复配置(missing_items)
            
            if auto_fix_result["success"]:
                # 重新检测
                final_missing = []
                for item in self.required_items:
                    item_path = os.path.join(self.steam_path, item)
                    if not os.path.exists(item_path):
                        final_missing.append(item)
                
                if not final_missing:
                    return {
                        "status": "success",
                        "message": "Configuration restored",
                        "missing_items": [],
                        "auto_fixed": True
                    }
                else:
                    return {
                        "status": "warning",
                        "message": "Partial configuration restored",
                        "missing_items": final_missing,
                        "auto_fixed": True
                    }
            else:
                return {
                    "status": "error",
                    "message": "Configuration incomplete",
                    "missing_items": missing_items,
                    "auto_fixed": False
                }
                
        except Exception as e:
            return {
                "status": "error",
                "message": "Detection failed",
                "missing_items": [],
                "auto_fixed": False,
                "error": str(e)
            }
    
    def _自动修复配置(self, missing_items):
        """
        自动修复配置文件
        
        Args:
            missing_items (list): 缺失的项目列表
            
        Returns:
            dict: 修复结果
        """
        try:
            # 检查配置文件.zip是否存在
            if not os.path.exists(self.config_zip_path):
                return {"success": False, "message": "Config archive not found"}
            
            # 解压配置文件到Steam目录
            with zipfile.ZipFile(self.config_zip_path, 'r') as zip_ref:
                # 静默解压，覆盖现有文件
                zip_ref.extractall(self.steam_path)
            
            return {"success": True, "message": "Config restored"}
            
        except Exception as e:
            return {"success": False, "message": f"Restore failed: {str(e)}"}
    
    def 手动检测配置(self):
        """
        手动触发配置检测（用于设置页面按钮）
        
        Returns:
            dict: 检测结果（包含用户友好的消息）
        """
        result = self.检测配置文件()
        
        # 转换为用户友好的消息
        user_messages = {
            "Steam not detected": "未检测到Steam环境",
            "Configuration complete": "配置环境正常",
            "Configuration restored": "配置环境已自动修复",
            "Partial configuration restored": "配置环境部分修复",
            "Configuration incomplete": "配置环境不完整",
            "Detection failed": "检测过程出现问题"
        }
        
        user_message = user_messages.get(result["message"], result["message"])
        
        return {
            "status": result["status"],
            "message": user_message,
            "auto_fixed": result.get("auto_fixed", False),
            "details": {
                "steam_path_found": self.steam_path is not None
            }
        }

# 全局配置检测器实例
_config_detector = None

def 获取配置检测器():
    """获取配置检测器实例"""
    global _config_detector
    if _config_detector is None:
        _config_detector = Steam配置检测器()
    return _config_detector

def 首次启动检测():
    """
    首次启动时的静默配置检测
    完全静默，不输出任何日志
    """
    user_code = get_current_user_code()
    send_remote_log('info', '开始首次启动配置检测', {
        'function': '首次启动检测',
        'detection_type': 'silent'
    }, user_code)

    try:
        detector = 获取配置检测器()
        result = detector.检测配置文件()

        # 记录检测结果
        send_remote_log('info', '首次启动配置检测完成', {
            'function': '首次启动检测',
            'status': result.get('status', 'unknown'),
            'auto_fixed': result.get('auto_fixed', False),
            'missing_count': result.get('details', {}).get('missing_count', 0)
        }, user_code)

        return result
    except Exception as e:
        send_remote_log('warning', '首次启动配置检测异常', {
            'function': '首次启动检测',
            'error': str(e)
        }, user_code)
        return {"status": "error", "message": "Silent detection failed"}

def 手动配置检测():
    """
    手动配置检测（用于设置页面）

    Returns:
        dict: 用户友好的检测结果
    """
    user_code = get_current_user_code()
    send_remote_log('info', '开始手动配置检测', {
        'function': '手动配置检测',
        'detection_type': 'manual'
    }, user_code)

    try:
        detector = 获取配置检测器()
        result = detector.手动检测配置()

        send_remote_log('info', '手动配置检测完成', {
            'function': '手动配置检测',
            'status': result.get('status', 'unknown'),
            'auto_fixed': result.get('auto_fixed', False),
            'missing_count': result.get('details', {}).get('missing_count', 0),
            'steam_path_found': result.get('details', {}).get('steam_path_found', False)
        }, user_code)

        return result
    except Exception as e:
        send_remote_log('error', '手动配置检测异常', {
            'function': '手动配置检测',
            'error': str(e)
        }, user_code)
        return {
            "status": "error",
            "message": "检测过程出现异常",
            "auto_fixed": False,
            "details": {
                "missing_count": 0,
                "steam_path_found": False
            }
        }
