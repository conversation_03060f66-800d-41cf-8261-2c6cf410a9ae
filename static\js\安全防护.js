// 安全防护功能 - 禁用F12和鼠标右键
(function() {
    'use strict';

    // 禁用右键菜单
    document.addEventListener('contextmenu', function(e) {
        e.preventDefault();
        return false;
    });

    // 禁用F12、Ctrl+Shift+I、Ctrl+Shift+J、Ctrl+U等开发者工具快捷键
    document.addEventListener('keydown', function(e) {
        // F12
        if (e.keyCode === 123) {
            e.preventDefault();
            return false;
        }

        // Ctrl+Shift+I (开发者工具)
        if (e.ctrlKey && e.shiftKey && e.keyCode === 73) {
            e.preventDefault();
            return false;
        }

        // Ctrl+Shift+J (控制台)
        if (e.ctrlKey && e.shiftKey && e.keyCode === 74) {
            e.preventDefault();
            return false;
        }

        // Ctrl+U (查看源代码)
        if (e.ctrlKey && e.keyCode === 85) {
            e.preventDefault();
            return false;
        }

        // Ctrl+Shift+C (元素选择器)
        if (e.ctrlKey && e.shiftKey && e.keyCode === 67) {
            e.preventDefault();
            return false;
        }

        // Ctrl+Shift+K (控制台 - Firefox)
        if (e.ctrlKey && e.shiftKey && e.keyCode === 75) {
            e.preventDefault();
            return false;
        }
    });

    // 检测开发者工具是否打开（基于窗口尺寸变化）
    let devtools = {
        open: false,
        orientation: null
    };

    const threshold = 160;

    setInterval(function() {
        if (window.outerHeight - window.innerHeight > threshold ||
            window.outerWidth - window.innerWidth > threshold) {
            if (!devtools.open) {
                devtools.open = true;
                // 可以在这里添加检测到开发者工具打开时的处理逻辑
                if (typeof console !== 'undefined') {
                    console.clear();
                }
            }
        } else {
            devtools.open = false;
        }
    }, 500);

    // 清空控制台（保留warn方法用于特效调试）
    if (typeof console !== 'undefined') {
        console.clear();

        // 保存原始的warn方法
        const originalWarn = console.warn;

        // 重写console方法
        const noop = function() {};
        ['log', 'debug', 'info', 'error', 'assert', 'dir', 'dirxml',
         'group', 'groupEnd', 'time', 'timeEnd', 'count', 'trace', 'profile', 'profileEnd'].forEach(function(method) {
            console[method] = noop;
        });

        // 保留warn方法用于特效系统的错误提示
        console.warn = originalWarn;
    }

    // 防止通过iframe绕过限制
    if (window.top !== window.self) {
        window.top.location = window.self.location;
    }

})();
